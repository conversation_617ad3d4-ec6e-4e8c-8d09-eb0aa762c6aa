#!/bin/bash

# 生产环境部署脚本

set -e

echo "🚀 部署 ofun-email-unified 到生产环境..."

# 检查必要工具
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 未安装，请先安装 $1"
        exit 1
    fi
}

echo "📋 检查必要工具..."
check_command "wrangler"
check_command "node"
check_command "npm"

# 检查 wrangler 登录状态
echo "🔐 检查 Wrangler 登录状态..."
if ! wrangler whoami &> /dev/null; then
    echo "⚠️  请先登录 Wrangler: wrangler login"
    exit 1
fi

# 检查环境变量配置
echo "⚙️  检查环境变量配置..."

# 检查必要的 secrets
check_secret() {
    if ! wrangler secret list | grep -q "$1"; then
        echo "⚠️  Secret '$1' 未配置"
        return 1
    fi
    return 0
}

echo "检查必要的 secrets..."
MISSING_SECRETS=()

if ! check_secret "JWT_SECRET"; then
    MISSING_SECRETS+=("JWT_SECRET")
fi

# 检查 Cloudflare 邮件服务配置
EMAIL_CONFIGURED=false

# 检查 Send Email API 配置
if wrangler list | grep -q "send_email"; then
    echo "✅ Cloudflare Send Email API 已配置"
    EMAIL_CONFIGURED=true
fi

# 检查 Email Routing 配置
if check_secret "CLOUDFLARE_ACCOUNT_ID" && check_secret "CLOUDFLARE_API_TOKEN"; then
    echo "✅ Cloudflare Email Routing 已配置"
    EMAIL_CONFIGURED=true
fi

# 检查 SMTP Gateway 配置
if check_secret "CLOUDFLARE_SMTP_GATEWAY"; then
    echo "✅ Cloudflare SMTP Gateway 已配置"
    EMAIL_CONFIGURED=true
fi

if [ "$EMAIL_CONFIGURED" = false ]; then
    echo "⚠️  未检测到 Cloudflare 邮件服务配置"
    echo "💡 Cloudflare 全家桶邮件发送支持以下方案："
    echo ""
    echo "方案 1: Send Email API (推荐)"
    echo "   - 在 Cloudflare Dashboard 中配置 Send Email binding"
    echo ""
    echo "方案 2: Email Routing"
    echo "   - wrangler secret put CLOUDFLARE_ACCOUNT_ID"
    echo "   - wrangler secret put CLOUDFLARE_API_TOKEN"
    echo ""
    echo "方案 3: SMTP Gateway"
    echo "   - wrangler secret put CLOUDFLARE_SMTP_GATEWAY"
    echo ""
    echo "📚 详细配置请参考: docs/cloudflare-email-setup.md"
    echo ""
    read -p "继续部署 (邮件将使用降级处理)? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 用户取消了部署"
        exit 1
    fi
fi

if [ ${#MISSING_SECRETS[@]} -gt 0 ]; then
    echo "❌ 缺少必要的 secrets:"
    for secret in "${MISSING_SECRETS[@]}"; do
        echo "   - $secret"
    done
    echo ""
    echo "请使用以下命令配置:"
    for secret in "${MISSING_SECRETS[@]}"; do
        echo "   wrangler secret put $secret"
    done
    exit 1
fi

echo "✅ 所有必要的 secrets 已配置"

# 检查数据库配置
echo "🗄️  检查数据库配置..."
if ! wrangler d1 list | grep -q "ofun-email-db"; then
    echo "❌ 生产数据库 'ofun-email-db' 不存在"
    echo "请先创建数据库: wrangler d1 create ofun-email-db"
    exit 1
fi

echo "✅ 生产数据库已存在"

# 构建前端
echo "🎨 构建前端..."
npm run build

# 运行数据库迁移
echo "📊 运行数据库迁移..."
echo "⚠️  即将对生产数据库执行迁移，请确认："
read -p "继续执行生产数据库迁移? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 用户取消了数据库迁移"
    exit 1
fi

for migration in migrations/*.sql; do
    if [ -f "$migration" ]; then
        echo "执行迁移: $migration"
        wrangler d1 execute ofun-email-db --file="$migration"
    fi
done

echo "✅ 数据库迁移完成"

# 部署 Worker
echo "🚀 部署 Worker..."
wrangler deploy

echo "✅ Worker 部署完成"

# 验证部署
echo "🧪 验证部署..."
WORKER_URL=$(wrangler list | grep "ofun-email-unified" | awk '{print $2}')

if [ -z "$WORKER_URL" ]; then
    echo "⚠️  无法获取 Worker URL，请手动验证部署"
else
    echo "Worker URL: $WORKER_URL"
    
    # 测试健康检查
    echo "测试健康检查..."
    if curl -s "$WORKER_URL" > /dev/null; then
        echo "✅ Worker 响应正常"
    else
        echo "⚠️  Worker 可能未正常启动，请检查日志"
    fi
fi

echo ""
echo "🎉 生产环境部署完成！"
echo ""
echo "📊 部署信息:"
echo "- Worker: $WORKER_URL"
echo "- 数据库: ofun-email-db"
echo "- 邮件服务: 已配置"
echo ""
echo "🔧 后续步骤:"
echo "1. 配置 Cloudflare Email Routing (如果使用)"
echo "2. 设置域名 DNS 记录 (SPF: include:_spf.cloudflare.net)"
echo "3. 在 Cloudflare Dashboard 验证发送域名"
echo "4. 测试邮件发送功能"
echo "5. 配置 Cloudflare Analytics 监控"
echo ""
echo "📚 参考文档:"
echo "- Cloudflare 邮件配置: docs/cloudflare-email-setup.md"
echo "- 生产环境配置: docs/production-email-setup.md"
echo "- API 文档: docs/api.md"
echo ""
echo "🧪 测试命令:"
echo "curl -X POST \"$WORKER_URL/api/auth/send-verification\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"email\":\"<EMAIL>\",\"purpose\":\"测试\"}'"

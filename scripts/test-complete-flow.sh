#!/bin/bash

# 完整的邮件验证和注册流程测试

API_BASE="http://localhost:8787/api"
TEST_EMAIL="test-$(date +%s)@ofun.my"

echo "🧪 测试完整的邮件验证和注册流程..."
echo "测试邮箱: $TEST_EMAIL"
echo ""

# 1. 发送验证码
echo "1️⃣ 发送验证码..."
SEND_RESULT=$(curl -s -X POST "$API_BASE/auth/send-verification" \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"$TEST_EMAIL\",\"purpose\":\"注册\"}")

echo "响应: $SEND_RESULT"

if echo "$SEND_RESULT" | grep -q '"success":true'; then
    echo "✅ 验证码发送成功"
else
    echo "❌ 验证码发送失败"
    exit 1
fi

echo ""
echo "2️⃣ 请查看 Wrangler 控制台输出获取验证码，然后输入验证码:"
read -p "验证码: " VERIFICATION_CODE

if [ -z "$VERIFICATION_CODE" ]; then
    echo "❌ 验证码不能为空"
    exit 1
fi

# 3. 验证邮箱
echo ""
echo "3️⃣ 验证邮箱..."
VERIFY_RESULT=$(curl -s -X POST "$API_BASE/auth/verify-email" \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"$TEST_EMAIL\",\"code\":\"$VERIFICATION_CODE\"}")

echo "响应: $VERIFY_RESULT"

if echo "$VERIFY_RESULT" | grep -q '"success":true'; then
    echo "✅ 邮箱验证成功"
else
    echo "❌ 邮箱验证失败"
    exit 1
fi

# 4. 用户注册
echo ""
echo "4️⃣ 用户注册..."
REGISTER_RESULT=$(curl -s -X POST "$API_BASE/auth/register" \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"$TEST_EMAIL\",\"password\":\"password123\",\"username\":\"testuser\",\"verificationCode\":\"$VERIFICATION_CODE\"}")

echo "响应: $REGISTER_RESULT"

if echo "$REGISTER_RESULT" | grep -q '"success":true'; then
    echo "✅ 用户注册成功"
else
    echo "❌ 用户注册失败"
    exit 1
fi

# 5. 用户登录
echo ""
echo "5️⃣ 用户登录..."
LOGIN_RESULT=$(curl -s -X POST "$API_BASE/auth/login" \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"$TEST_EMAIL\",\"password\":\"password123\"}")

echo "响应: $LOGIN_RESULT"

if echo "$LOGIN_RESULT" | grep -q '"success":true'; then
    echo "✅ 用户登录成功"
    
    # 提取 token
    TOKEN=$(echo "$LOGIN_RESULT" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    echo "Token: ${TOKEN:0:20}..."
    
    # 6. 获取用户信息
    echo ""
    echo "6️⃣ 获取用户信息..."
    PROFILE_RESULT=$(curl -s -X GET "$API_BASE/user/profile" \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json")
    
    echo "响应: $PROFILE_RESULT"
    
    if echo "$PROFILE_RESULT" | grep -q '"success":true'; then
        echo "✅ 获取用户信息成功"
    else
        echo "❌ 获取用户信息失败"
    fi
else
    echo "❌ 用户登录失败"
fi

echo ""
echo "🎉 测试完成！"
echo ""
echo "📊 测试总结:"
echo "- 邮箱: $TEST_EMAIL"
echo "- 验证码: $VERIFICATION_CODE"
echo "- 所有功能都已测试"
echo ""
echo "💡 提示:"
echo "- 查看 Wrangler 控制台可以看到详细的邮件内容"
echo "- 如果安装了 MailHog，可以访问 http://localhost:8025 查看邮件"
echo "- 数据库中已保存用户信息和邮件记录"

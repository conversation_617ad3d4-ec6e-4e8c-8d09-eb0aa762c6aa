#!/bin/bash

# 启动完整的开发环境（前端 + 后端）

echo "🚀 启动 ofun-email-unified 完整开发环境..."

# 检查端口是否被占用
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️  端口 $1 已被占用"
        return 1
    fi
    return 0
}

# 清理函数
cleanup() {
    echo ""
    echo "🛑 停止所有服务..."
    
    # 杀死后台进程
    if [ ! -z "$WORKER_PID" ]; then
        kill $WORKER_PID 2>/dev/null || true
        echo "Worker 服务已停止"
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        echo "前端服务已停止"
    fi
    
    if [ ! -z "$MAILHOG_PID" ]; then
        kill $MAILHOG_PID 2>/dev/null || true
        echo "MailHog 已停止"
    fi
    
    exit 0
}

# 设置信号处理
trap cleanup INT TERM

echo "📋 检查端口可用性..."
if ! check_port 8787; then
    echo "请先停止占用端口 8787 的进程"
    exit 1
fi

if ! check_port 5874; then
    echo "请先停止占用端口 5874 的进程"
    exit 1
fi

# 启动 MailHog（如果可用）
if command -v mailhog &> /dev/null; then
    echo "📧 启动 MailHog..."
    mailhog > /dev/null 2>&1 &
    MAILHOG_PID=$!
    echo "MailHog 已启动 (PID: $MAILHOG_PID) - Web界面: http://localhost:8025"
else
    echo "⚠️  MailHog 未安装，邮件将仅在控制台显示"
fi

# 启动 Worker 服务
echo "🔧 启动 Worker API 服务..."
wrangler dev --env local --port 8787 > worker.log 2>&1 &
WORKER_PID=$!
echo "Worker 服务已启动 (PID: $WORKER_PID) - API: http://localhost:8787"

# 等待 Worker 启动
echo "⏳ 等待 Worker 服务启动..."
sleep 3

# 检查 Worker 是否启动成功
if ! curl -s http://localhost:8787 > /dev/null; then
    echo "❌ Worker 服务启动失败，请检查 worker.log"
    cleanup
    exit 1
fi

echo "✅ Worker 服务启动成功"

# 启动前端服务
echo "🎨 启动前端开发服务器..."
npm run dev > frontend.log 2>&1 &
FRONTEND_PID=$!
echo "前端服务已启动 (PID: $FRONTEND_PID) - 前端: http://localhost:5874"

# 等待前端启动
echo "⏳ 等待前端服务启动..."
sleep 5

# 检查前端是否启动成功
if ! curl -s http://localhost:5874 > /dev/null; then
    echo "❌ 前端服务启动失败，请检查 frontend.log"
    cleanup
    exit 1
fi

echo "✅ 前端服务启动成功"

echo ""
echo "🎉 所有服务启动完成！"
echo ""
echo "📊 服务状态:"
echo "- 前端应用: http://localhost:5874"
echo "- API 服务: http://localhost:8787"
if [ ! -z "$MAILHOG_PID" ]; then
    echo "- 邮件查看: http://localhost:8025"
fi
echo ""
echo "🧪 测试页面:"
echo "- 邮件验证测试: http://localhost:5874/test-auth"
echo "- 主页: http://localhost:5874"
echo ""
echo "📝 日志文件:"
echo "- Worker 日志: worker.log"
echo "- 前端日志: frontend.log"
echo ""
echo "💡 使用说明:"
echo "1. 访问测试页面进行功能测试"
echo "2. 验证码会显示在 Worker 控制台和 worker.log 中"
echo "3. 按 Ctrl+C 停止所有服务"
echo ""

# 实时显示 Worker 日志（包含验证码）
echo "📋 Worker 日志 (实时显示，包含验证码):"
echo "----------------------------------------"
tail -f worker.log &
TAIL_PID=$!

# 等待用户中断
wait

# 清理
kill $TAIL_PID 2>/dev/null || true
cleanup

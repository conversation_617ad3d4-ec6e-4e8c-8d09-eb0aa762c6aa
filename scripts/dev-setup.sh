#!/bin/bash

# ofun-email-unified 本地开发环境设置脚本

set -e

echo "🚀 设置 ofun-email-unified 本地开发环境..."

# 检查必要的工具
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 未安装，请先安装 $1"
        exit 1
    fi
}

echo "📋 检查必要工具..."
check_command "node"
check_command "npm"
check_command "wrangler"

# 检查 wrangler 是否已登录
echo "🔐 检查 Wrangler 登录状态..."
if ! wrangler whoami &> /dev/null; then
    echo "⚠️  请先登录 Wrangler: wrangler login"
    exit 1
fi

# 创建本地 D1 数据库
echo "🗄️  创建本地 D1 数据库..."
if ! wrangler d1 list | grep -q "ofun-email-db-local"; then
    echo "创建新的本地数据库..."
    wrangler d1 create ofun-email-db-local
else
    echo "本地数据库已存在"
fi

# 运行数据库迁移
echo "📊 运行数据库迁移..."
for migration in migrations/*.sql; do
    if [ -f "$migration" ]; then
        echo "执行迁移: $migration"
        wrangler d1 execute ofun-email-db-local --local --file="$migration"
    fi
done

# 检查是否需要安装 MailHog（可选的本地邮件服务器）
echo "📧 检查本地邮件服务器..."
if command -v mailhog &> /dev/null; then
    echo "✅ MailHog 已安装"
    echo "💡 提示: 运行 'mailhog' 启动本地邮件服务器 (http://localhost:8025)"
else
    echo "⚠️  MailHog 未安装（可选）"
    echo "💡 安装 MailHog 以查看本地发送的邮件:"
    echo "   macOS: brew install mailhog"
    echo "   Linux: 下载 https://github.com/mailhog/MailHog/releases"
    echo "   Windows: 下载 https://github.com/mailhog/MailHog/releases"
fi

# 创建本地环境配置文件
echo "⚙️  创建本地环境配置..."
cat > .env.local << EOF
# 本地开发环境配置
ENVIRONMENT=local
ADMIN_PASSWORD=admin123
JWT_SECRET=your-local-jwt-secret-key-change-this-in-production
DOMAIN=ofun.my

# GitHub OAuth (需要在 GitHub 创建应用)
GITHUB_CLIENT_ID=your-local-github-client-id
GITHUB_CLIENT_SECRET=your-local-github-client-secret
GITHUB_REDIRECT_URI=http://localhost:8787/api/auth/github/callback

# 本地邮件配置
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=<EMAIL>
SMTP_PASS=password

# 数据库
DATABASE_URL=.wrangler/state/v3/d1/miniflare-D1DatabaseObject/ofun-email-db-local.sqlite
EOF

echo "📝 创建开发脚本..."
cat > scripts/dev.sh << 'EOF'
#!/bin/bash

# 启动本地开发环境

echo "🚀 启动 ofun-email-unified 本地开发环境..."

# 启动 MailHog（如果已安装）
if command -v mailhog &> /dev/null; then
    echo "📧 启动 MailHog..."
    mailhog > /dev/null 2>&1 &
    MAILHOG_PID=$!
    echo "MailHog 已启动 (PID: $MAILHOG_PID) - Web界面: http://localhost:8025"
fi

# 启动 Wrangler 开发服务器
echo "🔧 启动 Wrangler 开发服务器..."
echo "API 服务: http://localhost:8787"
echo "邮件处理: 通过 wrangler dev 的邮件功能"
echo ""
echo "按 Ctrl+C 停止服务"

# 清理函数
cleanup() {
    echo ""
    echo "🛑 停止服务..."
    if [ ! -z "$MAILHOG_PID" ]; then
        kill $MAILHOG_PID 2>/dev/null || true
        echo "MailHog 已停止"
    fi
    exit 0
}

trap cleanup INT TERM

# 启动 Wrangler
wrangler dev --env local --port 8787
EOF

chmod +x scripts/dev.sh

echo "📋 创建测试脚本..."
cat > scripts/test-email.sh << 'EOF'
#!/bin/bash

# 测试邮件功能

API_BASE="http://localhost:8787/api"

echo "🧪 测试邮件功能..."

# 测试发送验证码
echo "1. 测试发送验证码..."
curl -X POST "$API_BASE/auth/send-verification" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","purpose":"注册"}' \
  | jq .

echo ""
echo "2. 请检查控制台输出或 MailHog (http://localhost:8025) 查看验证码"
echo "3. 使用验证码测试验证接口:"
echo "   curl -X POST \"$API_BASE/auth/verify-email\" -H \"Content-Type: application/json\" -d '{\"email\":\"<EMAIL>\",\"code\":\"YOUR_CODE\"}'"
EOF

chmod +x scripts/test-email.sh

echo "✅ 本地开发环境设置完成！"
echo ""
echo "📚 使用说明:"
echo "1. 启动开发环境: ./scripts/dev.sh"
echo "2. 测试邮件功能: ./scripts/test-email.sh"
echo "3. 查看邮件: http://localhost:8025 (如果安装了 MailHog)"
echo "4. API 文档: http://localhost:8787/api"
echo ""
echo "🔧 配置文件:"
echo "- 本地环境变量: .env.local"
echo "- Wrangler 配置: wrangler.toml"
echo ""
echo "📧 邮件配置:"
echo "- 本地开发: 输出到控制台 + MailHog"
echo "- 生产环境: Cloudflare Email Routing"
echo ""
echo "🎯 下一步:"
echo "1. 配置 GitHub OAuth (可选)"
echo "2. 配置生产环境的邮件发送"
echo "3. 运行 ./scripts/dev.sh 开始开发"

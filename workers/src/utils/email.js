// 邮件发送工具类
// 支持本地开发和生产环境的邮件发送

export class EmailService {
  constructor(env) {
    this.env = env;
    this.isLocal = env.ENVIRONMENT === 'local';
  }

  /**
   * 发送验证码邮件
   * @param {string} to 收件人邮箱
   * @param {string} code 验证码
   * @param {string} purpose 用途（注册、重置密码等）
   */
  async sendVerificationCode(to, code, purpose = '注册') {
    const subject = `【ofun.my】邮箱验证码`;
    const htmlContent = this.generateVerificationEmailHTML(code, purpose);
    const textContent = this.generateVerificationEmailText(code, purpose);

    return await this.sendEmail(to, subject, textContent, htmlContent);
  }

  /**
   * 发送邮件的核心方法
   * @param {string} to 收件人
   * @param {string} subject 主题
   * @param {string} text 纯文本内容
   * @param {string} html HTML内容
   */
  async sendEmail(to, subject, text, html) {
    if (this.isLocal) {
      return await this.sendEmailLocal(to, subject, text, html);
    } else {
      return await this.sendEmailProduction(to, subject, text, html);
    }
  }

  /**
   * 本地开发环境发送邮件（使用 MailHog 或控制台输出）
   */
  async sendEmailLocal(to, subject, text, html) {
    try {
      // 在本地开发环境，我们可以：
      // 1. 输出到控制台
      // 2. 使用 MailHog 等本地邮件服务器
      // 3. 使用 nodemailer 发送到真实邮箱

      console.log('=== 本地邮件发送 ===');
      console.log('收件人:', to);
      console.log('主题:', subject);
      console.log('内容:', text);
      console.log('==================');

      // 如果配置了本地 SMTP，尝试发送真实邮件
      if (this.env.SMTP_HOST && this.env.SMTP_HOST !== 'localhost') {
        return await this.sendEmailViaSMTP(to, subject, text, html);
      }

      // 模拟发送成功
      return {
        success: true,
        messageId: `local-${Date.now()}`,
        message: '本地环境邮件发送成功（控制台输出）'
      };
    } catch (error) {
      console.error('本地邮件发送失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生产环境发送邮件（使用 Cloudflare Email Workers）
   */
  async sendEmailProduction(to, subject, text, html) {
    try {
      const fromEmail = `noreply@${this.env.DOMAIN}`;

      // 使用 Cloudflare Email Workers 发送邮件
      return await this.sendEmailViaCloudflareWorkers(to, subject, text, html, fromEmail);
    } catch (error) {
      console.error('Cloudflare 邮件发送失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }



  /**
   * 通过 Cloudflare Email Workers 发送邮件
   * 基于官方示例: https://developers.cloudflare.com/email-routing/email-workers/send-email-workers/#example-worker
   */
  async sendEmailViaCloudflareWorkers(to, subject, text, html, from) {
    try {
      // 使用 Cloudflare Send Email API
      if (this.env.SEND_EMAIL) {
        // 构建邮件消息，按照官方示例格式
        const message = {
          from: {
            email: from,
            name: "ofun.my"
          },
          to: [
            {
              email: to
            }
          ],
          subject: subject,
          content: [
            {
              type: "text/plain",
              value: text
            },
            {
              type: "text/html",
              value: html
            }
          ]
        };

        // 发送邮件
        const response = await this.env.SEND_EMAIL.send(message);

        return {
          success: true,
          messageId: response.id || `cloudflare-${Date.now()}`,
          message: 'Cloudflare Send Email 发送成功'
        };
      } else {
        // 如果没有配置 SEND_EMAIL binding，使用降级处理
        console.log('Cloudflare Send Email 未配置，邮件内容:', {
          from, to, subject, text: text.substring(0, 100) + '...'
        });

        return {
          success: true,
          messageId: `cloudflare-log-${Date.now()}`,
          message: 'Cloudflare Send Email 未配置，已记录日志'
        };
      }
    } catch (error) {
      console.error('Cloudflare Send Email 发送失败:', error);

      // 降级处理：记录日志但不阻塞用户流程
      console.log('邮件发送降级处理，记录邮件内容:', {
        from, to, subject, text: text.substring(0, 100) + '...'
      });

      return {
        success: true,
        messageId: `cloudflare-fallback-${Date.now()}`,
        message: 'Cloudflare 邮件发送降级处理成功'
      };
    }
  }





  /**
   * 生成验证码邮件的 HTML 内容
   */
  generateVerificationEmailHTML(code, purpose) {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>邮箱验证码</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #007bff; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px; background: #f9f9f9; }
        .code { font-size: 32px; font-weight: bold; color: #007bff; text-align: center; 
                padding: 20px; background: white; border: 2px dashed #007bff; margin: 20px 0; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>ofun.my 邮箱验证</h1>
        </div>
        <div class="content">
            <h2>您好！</h2>
            <p>您正在进行${purpose}操作，请使用以下验证码完成验证：</p>
            <div class="code">${code}</div>
            <p><strong>注意事项：</strong></p>
            <ul>
                <li>验证码有效期为 10 分钟</li>
                <li>请勿将验证码告诉他人</li>
                <li>如果您没有进行此操作，请忽略此邮件</li>
            </ul>
        </div>
        <div class="footer">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>&copy; 2024 ofun.my - 临时邮箱服务</p>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * 生成验证码邮件的纯文本内容
   */
  generateVerificationEmailText(code, purpose) {
    return `
ofun.my 邮箱验证

您好！

您正在进行${purpose}操作，请使用以下验证码完成验证：

验证码：${code}

注意事项：
- 验证码有效期为 10 分钟
- 请勿将验证码告诉他人
- 如果您没有进行此操作，请忽略此邮件

此邮件由系统自动发送，请勿回复
© 2024 ofun.my - 临时邮箱服务
`;
  }

  /**
   * 构建标准邮件内容
   */
  buildEmailContent(from, to, subject, text, html) {
    return {
      from,
      to,
      subject,
      text,
      html,
      headers: {
        'X-Mailer': 'ofun.my Email Service',
        'X-Priority': '3'
      }
    };
  }

  /**
   * 构建符合 RFC 5322 标准的邮件内容
   * 用于 Cloudflare Email Workers
   */
  buildRFC5322Email(from, to, subject, text, html) {
    const boundary = `boundary_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const date = new Date().toUTCString();

    let emailContent = `From: ${from}\r\n`;
    emailContent += `To: ${to}\r\n`;
    emailContent += `Subject: ${subject}\r\n`;
    emailContent += `Date: ${date}\r\n`;
    emailContent += `MIME-Version: 1.0\r\n`;
    emailContent += `Content-Type: multipart/alternative; boundary="${boundary}"\r\n`;
    emailContent += `X-Mailer: ofun.my Email Service\r\n`;
    emailContent += `\r\n`;

    // 纯文本部分
    emailContent += `--${boundary}\r\n`;
    emailContent += `Content-Type: text/plain; charset=utf-8\r\n`;
    emailContent += `Content-Transfer-Encoding: 8bit\r\n`;
    emailContent += `\r\n`;
    emailContent += `${text}\r\n`;
    emailContent += `\r\n`;

    // HTML 部分
    emailContent += `--${boundary}\r\n`;
    emailContent += `Content-Type: text/html; charset=utf-8\r\n`;
    emailContent += `Content-Transfer-Encoding: 8bit\r\n`;
    emailContent += `\r\n`;
    emailContent += `${html}\r\n`;
    emailContent += `\r\n`;

    // 结束边界
    emailContent += `--${boundary}--\r\n`;

    return emailContent;
  }

  /**
   * 验证邮箱地址格式
   */
  static isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 生成随机验证码
   */
  static generateVerificationCode(length = 6) {
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length) - 1;
    return Math.floor(min + Math.random() * (max - min + 1)).toString();
  }
}

// 导出便捷函数
export async function sendVerificationEmail(env, email, code, purpose = '注册') {
  const emailService = new EmailService(env);
  return await emailService.sendVerificationCode(email, code, purpose);
}

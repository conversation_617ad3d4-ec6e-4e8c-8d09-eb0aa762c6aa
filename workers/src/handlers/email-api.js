// 邮箱相关 API 处理器
import { getUserFromRequest } from '../utils/auth.js';

export async function handleEmailAPI(request, env, corsHeaders, path) {
  switch (path) {
    case '/emails':
      return handleGetEmails(request, env, corsHeaders);
    case '/emails/count':
      return handleGetEmailCount(request, env, corsHeaders);
    case '/generate':
      return handleGenerateEmail(request, env, corsHeaders);
    case '/delete':
      return handleDeleteEmails(request, env, corsHeaders);
    default:
      if (path.startsWith('/emails/')) {
        return handleGetEmailById(request, env, corsHeaders, path);
      }
      return new Response(JSON.stringify({
        success: false,
        message: 'Email API endpoint not found'
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
  }
}

async function handleGetEmails(request, env, corsHeaders) {
  if (request.method !== 'GET') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取查询参数
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '20'), 100);
    const domain = url.searchParams.get('domain');
    const includeExpired = url.searchParams.get('includeExpired') === 'true';
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = 'WHERE user_id = ?';
    let params = [user.id];

    if (domain) {
      whereClause += ' AND domain = ?';
      params.push(domain);
    }

    if (!includeExpired) {
      whereClause += ' AND expire_at > datetime("now")';
    }

    // 获取邮箱总数
    const countResult = await env.DB.prepare(
      `SELECT COUNT(*) as total FROM emails ${whereClause}`
    ).bind(...params).first();

    // 获取邮箱列表
    const emails = await env.DB.prepare(
      `SELECT id, mail, domain, type, relation, created_at, updated_at, expire_at,
              (expire_at > datetime("now")) as is_active
       FROM emails ${whereClause}
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`
    ).bind(...params, limit, offset).all();

    // 获取每个邮箱的邮件数量
    const emailsWithCounts = await Promise.all(
      emails.results.map(async (email) => {
        const mailCount = await env.DB.prepare(
          'SELECT COUNT(*) as count FROM mail_messages WHERE email_id = ?'
        ).bind(email.id).first();

        return {
          id: email.id,
          mail: email.mail,
          domain: email.domain,
          fullEmail: `${email.mail}@${email.domain}`,
          type: email.type,
          relation: email.relation,
          isActive: Boolean(email.is_active),
          mailCount: mailCount.count,
          createdAt: email.created_at,
          updatedAt: email.updated_at,
          expireAt: email.expire_at
        };
      })
    );

    return new Response(JSON.stringify({
      success: true,
      emails: emailsWithCounts,
      pagination: {
        page: page,
        limit: limit,
        total: countResult.total,
        totalPages: Math.ceil(countResult.total / limit)
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Get emails error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取邮箱列表失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleGetEmailCount(request, env, corsHeaders) {
  if (request.method !== 'GET') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取各种统计数据
    const totalCount = await env.DB.prepare(
      'SELECT COUNT(*) as count FROM emails WHERE user_id = ?'
    ).bind(user.id).first();

    const activeCount = await env.DB.prepare(
      'SELECT COUNT(*) as count FROM emails WHERE user_id = ? AND expire_at > datetime("now")'
    ).bind(user.id).first();

    const expiredCount = await env.DB.prepare(
      'SELECT COUNT(*) as count FROM emails WHERE user_id = ? AND expire_at <= datetime("now")'
    ).bind(user.id).first();

    // 按域名统计
    const domainStats = await env.DB.prepare(
      'SELECT domain, COUNT(*) as count FROM emails WHERE user_id = ? GROUP BY domain ORDER BY count DESC'
    ).bind(user.id).all();

    // 今日生成数量
    const todayCount = await env.DB.prepare(
      'SELECT COUNT(*) as count FROM emails WHERE user_id = ? AND date(created_at) = date("now")'
    ).bind(user.id).first();

    // 本周生成数量
    const weekCount = await env.DB.prepare(
      'SELECT COUNT(*) as count FROM emails WHERE user_id = ? AND created_at >= datetime("now", "-7 days")'
    ).bind(user.id).first();

    return new Response(JSON.stringify({
      success: true,
      counts: {
        total: totalCount.count,
        active: activeCount.count,
        expired: expiredCount.count,
        today: todayCount.count,
        thisWeek: weekCount.count
      },
      domainStats: domainStats.results.map(stat => ({
        domain: stat.domain,
        count: stat.count
      }))
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Get email count error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取邮箱统计失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleGenerateEmail(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    // 验证用户身份
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 检查用户配额
    const credits = await env.DB.prepare(
      'SELECT * FROM user_credits WHERE user_id = ?'
    ).bind(user.id).first();

    if (!credits || credits.balance < 1) {
      return new Response(JSON.stringify({
        success: false,
        message: '配额不足'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const { domain, customName } = await request.json();
    
    if (!domain) {
      return new Response(JSON.stringify({
        success: false,
        message: '请选择域名'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 生成邮箱名称
    const emailName = customName || generateRandomEmailName();
    const fullEmail = `${emailName}@${domain}`;
    const secret = generateRandomSecret();

    // 保存到数据库
    const insertResult = await env.DB.prepare(`
      INSERT INTO emails (mail, domain, type, secret, user_id, generated_by_user, created_at, updated_at, expire_at)
      VALUES (?, ?, '用户生成', ?, ?, 1, datetime('now'), datetime('now'), datetime('now', '+30 days'))
    `).bind(emailName, domain, secret, user.id).run();

    // 扣除配额
    await env.DB.prepare(
      'UPDATE user_credits SET balance = balance - 1, total_spent = total_spent + 1, updated_at = datetime("now") WHERE user_id = ?'
    ).bind(user.id).run();

    // 记录交易
    await env.DB.prepare(`
      INSERT INTO credit_transactions (user_id, type, amount, balance_after, description, created_at)
      VALUES (?, 'spend', 1, ?, '生成临时邮箱', datetime('now'))
    `).bind(user.id, credits.balance - 1).run();

    return new Response(JSON.stringify({
      success: true,
      email: {
        id: insertResult.meta.last_row_id,
        fullEmail: fullEmail,
        secret: secret,
        domain: domain,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      },
      creditsRemaining: credits.balance - 1
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Generate email error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '生成邮箱失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleDeleteEmails(request, env, corsHeaders) {
  if (request.method !== 'DELETE') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const { emailIds } = await request.json();

    if (!emailIds || !Array.isArray(emailIds) || emailIds.length === 0) {
      return new Response(JSON.stringify({
        success: false,
        message: '请提供要删除的邮箱ID列表'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 验证所有邮箱都属于当前用户
    const placeholders = emailIds.map(() => '?').join(',');
    const ownedEmails = await env.DB.prepare(
      `SELECT id FROM emails WHERE id IN (${placeholders}) AND user_id = ?`
    ).bind(...emailIds, user.id).all();

    if (ownedEmails.results.length !== emailIds.length) {
      return new Response(JSON.stringify({
        success: false,
        message: '部分邮箱不属于您或不存在'
      }), {
        status: 403,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 删除邮箱（级联删除邮件）
    const deleteResult = await env.DB.prepare(
      `DELETE FROM emails WHERE id IN (${placeholders}) AND user_id = ?`
    ).bind(...emailIds, user.id).run();

    return new Response(JSON.stringify({
      success: true,
      message: `成功删除 ${deleteResult.changes || emailIds.length} 个邮箱`,
      deletedCount: deleteResult.changes || emailIds.length
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Delete emails error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '删除邮箱失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleGetEmailById(request, env, corsHeaders, path) {
  if (request.method !== 'GET') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 从路径中提取邮箱ID
    const emailId = path.split('/').pop();

    if (!emailId || isNaN(parseInt(emailId))) {
      return new Response(JSON.stringify({
        success: false,
        message: '无效的邮箱ID'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取邮箱详情
    const email = await env.DB.prepare(
      `SELECT id, mail, domain, type, relation, secret, created_at, updated_at, expire_at,
              (expire_at > datetime("now")) as is_active
       FROM emails
       WHERE id = ? AND user_id = ?`
    ).bind(emailId, user.id).first();

    if (!email) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱不存在或无权访问'
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取邮件统计
    const mailStats = await env.DB.prepare(
      'SELECT COUNT(*) as total_mails FROM mail_messages WHERE email_id = ?'
    ).bind(emailId).first();

    // 获取最近的邮件
    const recentMails = await env.DB.prepare(
      `SELECT id, message_id, from_address, subject, received_at
       FROM mail_messages
       WHERE email_id = ?
       ORDER BY received_at DESC
       LIMIT 5`
    ).bind(emailId).all();

    return new Response(JSON.stringify({
      success: true,
      email: {
        id: email.id,
        mail: email.mail,
        domain: email.domain,
        fullEmail: `${email.mail}@${email.domain}`,
        type: email.type,
        relation: email.relation,
        secret: email.secret,
        isActive: Boolean(email.is_active),
        createdAt: email.created_at,
        updatedAt: email.updated_at,
        expireAt: email.expire_at,
        stats: {
          totalMails: mailStats.total_mails
        },
        recentMails: recentMails.results.map(mail => ({
          id: mail.id,
          messageId: mail.message_id,
          fromAddress: mail.from_address,
          subject: mail.subject,
          receivedAt: mail.received_at
        }))
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Get email by ID error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取邮箱详情失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 生成随机邮箱名称
function generateRandomEmailName() {
  const adjectives = ['quick', 'bright', 'cool', 'smart', 'fast', 'nice', 'good', 'best'];
  const nouns = ['mail', 'box', 'user', 'temp', 'test', 'demo', 'sample'];
  const numbers = Math.floor(Math.random() * 9999);
  
  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const noun = nouns[Math.floor(Math.random() * nouns.length)];
  
  return `${adjective}${noun}${numbers}`;
}

// 生成随机密钥
function generateRandomSecret() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

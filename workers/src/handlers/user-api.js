// 用户相关 API 处理器
import { getUserFromRequest } from '../utils/auth.js';

export async function handleUserAPI(request, env, corsHeaders, path) {
  switch (path) {
    case '/user/profile':
      return handleUserProfile(request, env, corsHeaders);
    case '/user/credits':
      return handleUserCredits(request, env, corsHeaders);
    case '/user/transactions':
      return handleUserTransactions(request, env, corsHeaders);
    case '/user/generate-email':
      return handleUserGenerateEmail(request, env, corsHeaders);
    case '/redemption/redeem':
      return handleRedeemCode(request, env, corsHeaders);
    default:
      return new Response(JSON.stringify({
        success: false,
        message: 'User API endpoint not found'
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
  }
}

async function handleUserProfile(request, env, corsHeaders) {
  if (request.method !== 'GET') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取用户配额信息
    const credits = await env.DB.prepare(
      'SELECT * FROM user_credits WHERE user_id = ?'
    ).bind(user.id).first();

    return new Response(JSON.stringify({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        displayName: user.display_name,
        avatarUrl: user.avatar_url,
        emailVerified: user.email_verified,
        createdAt: user.created_at,
        lastLoginAt: user.last_login_at
      },
      credits: credits ? {
        balance: credits.balance,
        totalEarned: credits.total_earned,
        totalSpent: credits.total_spent
      } : null
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Get user profile error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取用户信息失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleUserCredits(request, env, corsHeaders) {
  if (request.method !== 'GET') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取用户配额信息
    const credits = await env.DB.prepare(
      'SELECT * FROM user_credits WHERE user_id = ?'
    ).bind(user.id).first();

    if (!credits) {
      // 如果没有配额记录，创建一个默认的
      await env.DB.prepare(
        'INSERT INTO user_credits (user_id, balance, total_earned, total_spent, created_at, updated_at) VALUES (?, 0, 0, 0, datetime("now"), datetime("now"))'
      ).bind(user.id).run();

      return new Response(JSON.stringify({
        success: true,
        credits: {
          balance: 0,
          totalEarned: 0,
          totalSpent: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      credits: {
        balance: credits.balance,
        totalEarned: credits.total_earned,
        totalSpent: credits.total_spent,
        createdAt: credits.created_at,
        updatedAt: credits.updated_at
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Get user credits error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取配额信息失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleUserTransactions(request, env, corsHeaders) {
  if (request.method !== 'GET') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取查询参数
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '20'), 100); // 最大100条
    const type = url.searchParams.get('type'); // 可选的交易类型过滤
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = 'WHERE user_id = ?';
    let params = [user.id];

    if (type && ['earn', 'spend', 'refund', 'admin_adjust'].includes(type)) {
      whereClause += ' AND type = ?';
      params.push(type);
    }

    // 获取交易记录总数
    const countResult = await env.DB.prepare(
      `SELECT COUNT(*) as total FROM credit_transactions ${whereClause}`
    ).bind(...params).first();

    // 获取交易记录
    const transactions = await env.DB.prepare(
      `SELECT * FROM credit_transactions ${whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?`
    ).bind(...params, limit, offset).all();

    return new Response(JSON.stringify({
      success: true,
      transactions: transactions.results.map(tx => ({
        id: tx.id,
        type: tx.type,
        amount: tx.amount,
        balanceAfter: tx.balance_after,
        description: tx.description,
        referenceType: tx.reference_type,
        referenceId: tx.reference_id,
        metadata: tx.metadata ? JSON.parse(tx.metadata) : null,
        createdAt: tx.created_at
      })),
      pagination: {
        page: page,
        limit: limit,
        total: countResult.total,
        totalPages: Math.ceil(countResult.total / limit)
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Get user transactions error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取交易记录失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleUserGenerateEmail(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取用户配额
    const credits = await env.DB.prepare(
      'SELECT * FROM user_credits WHERE user_id = ?'
    ).bind(user.id).first();

    if (!credits || credits.balance < 1) {
      return new Response(JSON.stringify({
        success: false,
        message: '配额不足，无法生成邮箱'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const { domain, customName } = await request.json();

    if (!domain) {
      return new Response(JSON.stringify({
        success: false,
        message: '请选择域名'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 验证域名是否在允许列表中
    const allowedDomains = ['ofun.my', 'ofun.io', 'temp.email', 'example.com']; // 可以从配置中读取
    if (!allowedDomains.includes(domain)) {
      return new Response(JSON.stringify({
        success: false,
        message: '不支持的域名'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 生成邮箱名称
    const emailName = customName || generateRandomEmailName();
    const fullEmail = `${emailName}@${domain}`;
    const secret = generateRandomSecret();

    // 检查邮箱是否已存在
    const existingEmail = await env.DB.prepare(
      'SELECT id FROM emails WHERE mail = ? AND domain = ?'
    ).bind(emailName, domain).first();

    if (existingEmail) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱已存在，请选择其他名称'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 保存到数据库
    const insertResult = await env.DB.prepare(`
      INSERT INTO emails (mail, domain, type, secret, user_id, generated_by_user, created_at, updated_at, expire_at)
      VALUES (?, ?, '用户生成', ?, ?, 1, datetime('now'), datetime('now'), datetime('now', '+30 days'))
    `).bind(emailName, domain, secret, user.id).run();

    // 扣除配额
    await env.DB.prepare(
      'UPDATE user_credits SET balance = balance - 1, total_spent = total_spent + 1, updated_at = datetime("now") WHERE user_id = ?'
    ).bind(user.id).run();

    // 记录交易
    await env.DB.prepare(`
      INSERT INTO credit_transactions (user_id, type, amount, balance_after, description, reference_type, reference_id, created_at)
      VALUES (?, 'spend', 1, ?, '生成临时邮箱', 'email_generation', ?, datetime('now'))
    `).bind(user.id, credits.balance - 1, insertResult.meta.last_row_id).run();

    return new Response(JSON.stringify({
      success: true,
      email: {
        id: insertResult.meta.last_row_id,
        fullEmail: fullEmail,
        secret: secret,
        domain: domain,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      },
      creditsRemaining: credits.balance - 1
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Generate email error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '生成邮箱失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 生成随机邮箱名称
function generateRandomEmailName() {
  const adjectives = ['quick', 'bright', 'cool', 'smart', 'fast', 'nice', 'good', 'best'];
  const nouns = ['mail', 'box', 'user', 'temp', 'test', 'demo', 'sample'];
  const numbers = Math.floor(Math.random() * 9999);

  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const noun = nouns[Math.floor(Math.random() * nouns.length)];

  return `${adjective}${noun}${numbers}`;
}

// 生成随机密钥
function generateRandomSecret() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

async function handleRedeemCode(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const { code } = await request.json();
    
    if (!code) {
      return new Response(JSON.stringify({
        success: false,
        message: '请输入兑换码'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 查找兑换码
    const redemptionCode = await env.DB.prepare(
      'SELECT * FROM redemption_codes WHERE code = ? AND is_active = 1 AND (expires_at IS NULL OR expires_at > datetime("now"))'
    ).bind(code).first();

    if (!redemptionCode) {
      return new Response(JSON.stringify({
        success: false,
        message: '兑换码无效或已过期'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 检查使用次数限制
    if (redemptionCode.max_uses > 0) {
      const usageCount = await env.DB.prepare(
        'SELECT COUNT(*) as count FROM redemption_code_uses WHERE code_id = ?'
      ).bind(redemptionCode.id).first();

      if (usageCount.count >= redemptionCode.max_uses) {
        return new Response(JSON.stringify({
          success: false,
          message: '兑换码使用次数已达上限'
        }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }
    }

    // 检查用户是否已使用过此兑换码
    const userUsage = await env.DB.prepare(
      'SELECT * FROM redemption_code_uses WHERE code_id = ? AND user_id = ?'
    ).bind(redemptionCode.id, user.id).first();

    if (userUsage) {
      return new Response(JSON.stringify({
        success: false,
        message: '您已使用过此兑换码'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取当前配额
    const credits = await env.DB.prepare(
      'SELECT * FROM user_credits WHERE user_id = ?'
    ).bind(user.id).first();

    const currentBalance = credits ? credits.balance : 0;
    const newBalance = currentBalance + redemptionCode.credits;

    // 更新用户配额
    if (credits) {
      await env.DB.prepare(
        'UPDATE user_credits SET balance = ?, total_earned = total_earned + ?, updated_at = datetime("now") WHERE user_id = ?'
      ).bind(newBalance, redemptionCode.credits, user.id).run();
    } else {
      await env.DB.prepare(
        'INSERT INTO user_credits (user_id, balance, total_earned, total_spent, created_at, updated_at) VALUES (?, ?, ?, 0, datetime("now"), datetime("now"))'
      ).bind(user.id, newBalance, redemptionCode.credits).run();
    }

    // 记录兑换码使用
    await env.DB.prepare(
      'INSERT INTO redemption_code_uses (code_id, user_id, credits_awarded, used_at) VALUES (?, ?, ?, datetime("now"))'
    ).bind(redemptionCode.id, user.id, redemptionCode.credits).run();

    // 记录交易
    await env.DB.prepare(
      'INSERT INTO credit_transactions (user_id, type, amount, balance_after, description, created_at) VALUES (?, "earn", ?, ?, ?, datetime("now"))'
    ).bind(user.id, redemptionCode.credits, newBalance, `兑换码: ${code}`).run();

    return new Response(JSON.stringify({
      success: true,
      message: '兑换成功',
      creditsAwarded: redemptionCode.credits,
      newBalance: newBalance
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Redeem code error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '兑换失败，请稍后重试'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

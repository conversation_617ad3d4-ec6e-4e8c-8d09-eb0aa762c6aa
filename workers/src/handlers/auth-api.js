// 认证相关 API 处理器
import { generateJWT, hashPassword, verifyPassword } from '../utils/auth.js';
import { EmailService } from '../utils/email.js';

export async function handleAuthAPI(request, env, corsHeaders, path) {
  switch (path) {
    case '/auth/register':
      return handleUserRegister(request, env, corsHeaders);
    case '/auth/send-verification':
      return handleSendVerification(request, env, corsHeaders);
    case '/auth/verify-email':
      return handleVerifyEmail(request, env, corsHeaders);
    case '/auth/login':
      return handleUserLogin(request, env, corsHeaders);
    case '/auth/logout':
      return handleUserLogout(request, env, corsHeaders);
    case '/auth/refresh':
      return handleRefreshToken(request, env, corsHeaders);
    case '/auth/github':
      return handleGitHubOAuth(request, env, corsHeaders);
    case '/auth/github/callback':
      return handleGitHubCallback(request, env, corsHeaders);
    default:
      return new Response(JSON.stringify({
        success: false,
        message: 'Auth endpoint not found'
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
  }
}

async function handleUserRegister(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const { email, password, username, verificationCode } = await request.json();
    
    if (!email || !password) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱和密码不能为空'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱格式不正确'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 验证密码长度
    if (password.length < 6) {
      return new Response(JSON.stringify({
        success: false,
        message: '密码长度至少6位'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 检查邮箱是否已存在
    const existingUser = await env.DB.prepare(
      'SELECT id FROM users WHERE email = ?'
    ).bind(email).first();

    if (existingUser) {
      return new Response(JSON.stringify({
        success: false,
        message: '该邮箱已被注册'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 如果提供了验证码，验证验证码
    if (verificationCode) {
      const verification = await env.DB.prepare(
        'SELECT * FROM email_verifications WHERE email = ? AND code = ? AND expires_at > datetime("now") AND used = 0'
      ).bind(email, verificationCode).first();

      if (!verification) {
        return new Response(JSON.stringify({
          success: false,
          message: '验证码无效或已过期'
        }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      // 标记验证码为已使用
      await env.DB.prepare(
        'UPDATE email_verifications SET used = 1 WHERE id = ?'
      ).bind(verification.id).run();

      // 创建用户（已验证邮箱）
      const hashedPassword = await hashPassword(password);
      const insertResult = await env.DB.prepare(`
        INSERT INTO users (email, username, password_hash, email_verified, created_at)
        VALUES (?, ?, ?, 1, datetime('now'))
      `).bind(email, username || null, hashedPassword).run();

      const userId = insertResult.meta.last_row_id;

      // 为新用户创建配额记录
      await env.DB.prepare(`
        INSERT INTO user_credits (user_id, balance, total_earned, total_spent, created_at, updated_at)
        VALUES (?, 5, 5, 0, datetime('now'), datetime('now'))
      `).bind(userId).run();

      // 记录配额获得交易
      await env.DB.prepare(`
        INSERT INTO credit_transactions (user_id, type, amount, balance_after, description, created_at)
        VALUES (?, 'earn', 5, 5, '新用户注册奖励', datetime('now'))
      `).bind(userId).run();

      return new Response(JSON.stringify({
        success: true,
        message: '注册成功！'
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    } else {
      // 发送验证码
      const verificationCode = EmailService.generateVerificationCode(6);

      // 保存验证码到数据库
      await env.DB.prepare(`
        INSERT INTO email_verifications (email, code, expires_at, created_at)
        VALUES (?, ?, datetime('now', '+10 minutes'), datetime('now'))
      `).bind(email, verificationCode).run();

      // 发送验证邮件
      const emailService = new EmailService(env);
      const emailResult = await emailService.sendVerificationCode(email, verificationCode, '注册');

      if (!emailResult.success) {
        console.error('邮件发送失败:', emailResult.error);
        // 即使邮件发送失败，也返回成功，避免暴露系统信息
      }

      return new Response(JSON.stringify({
        success: true,
        message: '验证码已发送到您的邮箱，请查收',
        requiresVerification: true
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('User register error:', error);

    // 根据错误类型返回具体的错误信息
    let errorMessage = '注册失败，请稍后重试';
    let statusCode = 500;

    if (error.message && error.message.includes('UNIQUE constraint failed')) {
      if (error.message.includes('users.email')) {
        errorMessage = '该邮箱已被注册，请使用其他邮箱';
        statusCode = 400;
      } else if (error.message.includes('users.username')) {
        errorMessage = '该用户名已被使用，请选择其他用户名';
        statusCode = 400;
      }
    } else if (error.message && error.message.includes('验证码')) {
      errorMessage = error.message;
      statusCode = 400;
    }

    return new Response(JSON.stringify({
      success: false,
      message: errorMessage
    }), {
      status: statusCode,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 发送验证码（独立接口）
async function handleSendVerification(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const { email, purpose = '注册' } = await request.json();

    if (!email) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱不能为空'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 验证邮箱格式
    if (!EmailService.isValidEmail(email)) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱格式不正确'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 检查是否频繁发送（防止滥用）
    const recentVerification = await env.DB.prepare(
      'SELECT * FROM email_verifications WHERE email = ? AND created_at > datetime("now", "-1 minute") ORDER BY created_at DESC LIMIT 1'
    ).bind(email).first();

    if (recentVerification) {
      return new Response(JSON.stringify({
        success: false,
        message: '请勿频繁发送验证码，请稍后再试'
      }), {
        status: 429,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 生成验证码
    const verificationCode = EmailService.generateVerificationCode(6);

    // 保存验证码到数据库
    await env.DB.prepare(`
      INSERT INTO email_verifications (email, code, expires_at, created_at)
      VALUES (?, ?, datetime('now', '+10 minutes'), datetime('now'))
    `).bind(email, verificationCode).run();

    // 发送验证邮件
    const emailService = new EmailService(env);
    const emailResult = await emailService.sendVerificationCode(email, verificationCode, purpose);

    if (!emailResult.success) {
      console.error('邮件发送失败:', emailResult.error);
    }

    return new Response(JSON.stringify({
      success: true,
      message: '验证码已发送到您的邮箱，请查收'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Send verification error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '发送验证码失败，请稍后重试'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 验证邮箱（独立接口）
async function handleVerifyEmail(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const { email, code } = await request.json();

    if (!email || !code) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱和验证码不能为空'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 验证验证码
    const verification = await env.DB.prepare(
      'SELECT * FROM email_verifications WHERE email = ? AND code = ? AND expires_at > datetime("now") AND used = 0'
    ).bind(email, code).first();

    if (!verification) {
      return new Response(JSON.stringify({
        success: false,
        message: '验证码无效或已过期'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 标记验证码为已使用
    await env.DB.prepare(
      'UPDATE email_verifications SET used = 1 WHERE id = ?'
    ).bind(verification.id).run();

    return new Response(JSON.stringify({
      success: true,
      message: '邮箱验证成功'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Verify email error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '验证失败，请稍后重试'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleUserLogin(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const { email, password } = await request.json();
    
    if (!email || !password) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱和密码不能为空'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 查找用户
    const user = await env.DB.prepare(
      'SELECT * FROM users WHERE email = ?'
    ).bind(email).first();

    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱或密码错误'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 验证密码
    const isValidPassword = await verifyPassword(password, user.password_hash);
    if (!isValidPassword) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱或密码错误'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 检查邮箱是否已验证
    if (!user.email_verified) {
      return new Response(JSON.stringify({
        success: false,
        message: '请先验证您的邮箱'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 更新最后登录时间
    await env.DB.prepare(
      'UPDATE users SET last_login_at = datetime("now") WHERE id = ?'
    ).bind(user.id).run();

    // 生成 JWT 令牌
    const token = await generateJWT({
      userId: user.id,
      email: user.email,
      exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7天
    }, env.JWT_SECRET || 'default-secret');

    const refreshToken = await generateJWT({
      userId: user.id,
      type: 'refresh',
      exp: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30天
    }, env.JWT_SECRET || 'default-secret');

    // 获取用户配额信息
    const credits = await env.DB.prepare(
      'SELECT * FROM user_credits WHERE user_id = ?'
    ).bind(user.id).first();

    return new Response(JSON.stringify({
      success: true,
      token: token,
      refreshToken: refreshToken,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        displayName: user.display_name,
        avatarUrl: user.avatar_url,
        emailVerified: user.email_verified,
        createdAt: user.created_at,
        lastLoginAt: user.last_login_at
      },
      credits: credits ? {
        balance: credits.balance,
        totalEarned: credits.total_earned,
        totalSpent: credits.total_spent
      } : null
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('User login error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '登录失败，请稍后重试'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 其他认证函数的占位符
async function handleUserLogout(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: true,
    message: '登出成功'
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleRefreshToken(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '刷新令牌功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleGitHubOAuth(request, env, corsHeaders) {
  if (request.method !== 'GET') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    // GitHub OAuth 配置
    const clientId = env.GITHUB_CLIENT_ID;
    const redirectUri = env.GITHUB_REDIRECT_URI || 'https://your-domain.com/api/auth/github/callback';
    
    if (!clientId) {
      return new Response(JSON.stringify({
        success: false,
        message: 'GitHub OAuth 未配置'
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 生成随机 state
    const state = crypto.randomUUID();
    
    // 构建 GitHub OAuth URL
    const authUrl = new URL('https://github.com/login/oauth/authorize');
    authUrl.searchParams.set('client_id', clientId);
    authUrl.searchParams.set('redirect_uri', redirectUri);
    authUrl.searchParams.set('scope', 'user:email');
    authUrl.searchParams.set('state', state);

    return new Response(JSON.stringify({
      success: true,
      authUrl: authUrl.toString(),
      state: state
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('GitHub OAuth error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: 'GitHub OAuth 初始化失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleGitHubCallback(request, env, corsHeaders) {
  // GitHub OAuth 回调处理 - 这里需要完整实现
  return new Response(JSON.stringify({
    success: false,
    message: 'GitHub OAuth 回调功能需要完整实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

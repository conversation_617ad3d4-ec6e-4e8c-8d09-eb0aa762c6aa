// 系统公告相关 API 处理器
import { getUserFromRequest } from '../utils/auth.js';

export async function handleAnnouncementAPI(request, env, corsHeaders, path) {
  switch (path) {
    case '/announcements':
      return handleGetAnnouncements(request, env, corsHeaders);
    default:
      return new Response(JSON.stringify({
        success: false,
        message: 'Announcement API endpoint not found'
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
  }
}

async function handleGetAnnouncements(request, env, corsHeaders) {
  if (request.method !== 'GET') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    // 获取查询参数
    const url = new URL(request.url);
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '10'), 50);
    const type = url.searchParams.get('type'); // 可选的公告类型过滤

    // 检查用户是否登录（用于确定目标用户群体）
    const user = await getUserFromRequest(request, env);
    
    // 构建查询条件
    let whereClause = 'WHERE is_active = 1';
    let params = [];
    
    if (type && ['info', 'warning', 'success', 'error'].includes(type)) {
      whereClause += ' AND type = ?';
      params.push(type);
    }
    
    // 根据用户状态过滤公告
    if (user) {
      whereClause += ' AND (target_users = "all" OR target_users = "registered")';
    } else {
      whereClause += ' AND (target_users = "all" OR target_users = "anonymous")';
    }

    // 获取公告列表
    const announcements = await env.DB.prepare(
      `SELECT id, title, content, type, priority, created_at, updated_at
       FROM system_announcements 
       ${whereClause} 
       ORDER BY priority DESC, created_at DESC 
       LIMIT ?`
    ).bind(...params, limit).all();

    return new Response(JSON.stringify({
      success: true,
      announcements: announcements.results.map(announcement => ({
        id: announcement.id,
        title: announcement.title,
        content: announcement.content,
        type: announcement.type,
        priority: announcement.priority,
        createdAt: announcement.created_at,
        updatedAt: announcement.updated_at
      }))
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Get announcements error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取系统公告失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

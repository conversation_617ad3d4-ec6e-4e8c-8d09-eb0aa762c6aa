# TempMail 配置指南

## 1. GitHub OAuth 配置

### 1.1 GitHub 应用设置
在你的 GitHub OAuth 应用中，需要设置以下回调 URL：
```
https://tempmail.htmljs.cn/auth/github/callback
```

### 1.2 Cloudflare Workers 环境变量配置

在 Cloudflare Dashboard 中，进入你的 Worker 设置页面，配置以下环境变量：

**Secrets (敏感信息):**
```
GITHUB_CLIENT_SECRET = c3a6bc53c55b1da20f11183bfc76b2641e11796b
JWT_SECRET = your-jwt-secret-key-here
```

**Variables (普通变量):**
```
GITHUB_CLIENT_ID = ********************
GITHUB_REDIRECT_URI = https://tempmail.htmljs.cn/auth/github/callback
ENVIRONMENT = production
```

## 2. 邮件发送配置 (MailChannels)

### 2.1 域名验证
为了使用 MailChannels 发送邮件，你需要：

1. 在你的域名 DNS 中添加以下 TXT 记录：
```
Name: _mailchannels
Value: v=mc1 cfid=your-cloudflare-account-id
```

2. 添加 SPF 记录：
```
Name: @
Type: TXT
Value: v=spf1 include:relay.mailchannels.net ~all
```

3. 添加 DKIM 记录（可选，提高邮件送达率）：
```
Name: mailchannels._domainkey
Type: TXT
Value: v=DKIM1; k=rsa; p=your-dkim-public-key
```

### 2.2 发件人域名配置
在代码中，邮件发送使用的域名是 `tempmail.htmljs.cn`，你需要：
1. 确保该域名指向 Cloudflare
2. 在该域名下配置上述 DNS 记录

## 3. 邮件接收配置

### 3.1 MX 记录配置
在你的域名 DNS 中添加 MX 记录：
```
Name: @
Type: MX
Priority: 10
Value: isaac.mx.cloudflare.net
```

### 3.2 邮件路由配置
在 Cloudflare Dashboard 中：
1. 进入 Email Routing 页面
2. 添加 Catch-All 规则
3. 目标设置为你的 Worker: `ofun-email-unified`

## 4. 部署步骤

### 4.1 安装依赖
```bash
npm install
npm install -g wrangler
```

### 4.2 登录 Cloudflare
```bash
wrangler login
```

### 4.3 部署应用
```bash
chmod +x deploy-workers.sh
./deploy-workers.sh
```

### 4.4 配置环境变量
在 Cloudflare Dashboard 中配置上述环境变量。

## 5. 测试验证

### 5.1 测试 GitHub 登录
1. 访问前端应用
2. 点击 "使用 GitHub 登录"
3. 完成 OAuth 流程

### 5.2 测试邮件注册
1. 访问注册页面
2. 输入邮箱地址
3. 检查是否收到验证码邮件

### 5.3 测试邮件接收
1. 生成临时邮箱
2. 向该邮箱发送测试邮件
3. 检查是否能正常接收

## 6. 常见问题

### 6.1 GitHub 登录失败
- 检查 GitHub OAuth 应用的回调 URL 配置
- 确认 GITHUB_CLIENT_ID 和 GITHUB_CLIENT_SECRET 正确
- 检查 GITHUB_REDIRECT_URI 环境变量

### 6.2 验证码邮件发送失败
- 检查 MailChannels DNS 记录配置
- 确认发件人域名正确
- 查看 Worker 日志中的错误信息

### 6.3 邮件接收失败
- 检查 MX 记录配置
- 确认邮件路由规则正确
- 检查 Worker 是否正常运行

## 7. 监控和日志

### 7.1 Worker 日志
在 Cloudflare Dashboard 中查看 Worker 的实时日志。

### 7.2 邮件路由日志
在 Email Routing 页面查看邮件处理日志。

### 7.3 错误监控
建议配置错误监控和告警，及时发现问题。

# ofun-email-unified 本地开发指南

## 快速开始

### 1. 环境准备

确保你已安装以下工具：

```bash
# Node.js (推荐 18+)
node --version

# npm
npm --version

# Wrangler CLI
npm install -g wrangler
wrangler --version

# 登录 Cloudflare
wrangler login
```

### 2. 一键设置开发环境

```bash
# 运行设置脚本
npm run dev:setup

# 或手动运行
./scripts/dev-setup.sh
```

### 3. 启动开发服务器

```bash
# 启动完整开发环境（包括 MailHog）
npm run dev:start

# 或仅启动 Worker
npm run dev:worker

# 启动前端开发服务器
npm run dev
```

## 开发环境架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │  Cloudflare     │    │   本地邮件      │
│  localhost:5173 │◄──►│    Worker       │◄──►│   MailHog       │
│                 │    │ localhost:8787  │    │ localhost:8025  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   本地 D1       │
                       │    数据库       │
                       └─────────────────┘
```

## 邮件功能测试

### 1. 测试邮件发送

```bash
# 运行邮件测试脚本
npm run dev:test-email

# 或手动测试
curl -X POST "http://localhost:8787/api/auth/send-verification" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","purpose":"注册"}'
```

### 2. 查看邮件

- **控制台输出**: 查看 Wrangler 开发服务器的控制台
- **MailHog Web界面**: http://localhost:8025
- **邮件日志**: 查看数据库中的 `email_logs` 表

### 3. 测试邮件接收

```bash
# 模拟接收邮件（需要配置 Email Routing）
curl -X POST "http://localhost:8787" \
  -H "Content-Type: application/json" \
  -d '{
    "from": "<EMAIL>",
    "to": "<EMAIL>",
    "subject": "Test Email",
    "raw": "From: <EMAIL>\nTo: <EMAIL>\nSubject: Test Email\n\nVerification code: 123456"
  }'
```

## API 接口文档

### 认证相关

#### 发送验证码
```http
POST /api/auth/send-verification
Content-Type: application/json

{
  "email": "<EMAIL>",
  "purpose": "注册"
}
```

#### 验证邮箱
```http
POST /api/auth/verify-email
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456"
}
```

#### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "username": "username",
  "verificationCode": "123456"
}
```

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 用户相关

#### 获取用户信息
```http
GET /api/user/profile
Authorization: Bearer <token>
```

## 数据库操作

### 本地数据库

```bash
# 查看数据库
wrangler d1 execute ofun-email-db-local --local --command "SELECT * FROM users"

# 运行迁移
npm run db:migrate

# 重置数据库
rm -rf .wrangler/state/v3/d1/
npm run db:migrate
```

### 生产数据库

```bash
# 查看生产数据库
wrangler d1 execute ofun-email-db --command "SELECT COUNT(*) FROM users"

# 运行生产迁移
npm run db:migrate:prod
```

## 配置说明

### 环境变量

#### 本地开发 (.env.local)
```env
ENVIRONMENT=local
ADMIN_PASSWORD=admin123
JWT_SECRET=your-local-jwt-secret
DOMAIN=ofun.my
SMTP_HOST=localhost
SMTP_PORT=1025
```

#### 生产环境 (Cloudflare Secrets)
```bash
wrangler secret put JWT_SECRET
wrangler secret put SENDGRID_API_KEY
wrangler secret put GITHUB_CLIENT_SECRET
```

### Wrangler 配置

- **本地环境**: `[env.local]` 配置
- **生产环境**: 默认配置
- **数据库**: D1 数据库绑定
- **变量**: 环境变量配置

## 常见问题

### 1. 邮件发送失败

**问题**: 邮件发送返回错误

**解决方案**:
1. 检查 SMTP 配置
2. 确认 MailHog 是否启动
3. 查看控制台错误日志
4. 验证邮箱格式

### 2. 验证码无效

**问题**: 验证码验证失败

**解决方案**:
1. 检查验证码是否过期（10分钟）
2. 确认验证码是否已使用
3. 验证数据库连接
4. 检查时间同步

### 3. 数据库连接失败

**问题**: D1 数据库无法连接

**解决方案**:
1. 确认数据库已创建
2. 运行数据库迁移
3. 检查 wrangler.toml 配置
4. 重启开发服务器

### 4. CORS 错误

**问题**: 前端请求被 CORS 阻止

**解决方案**:
1. 检查 Worker 的 CORS 头设置
2. 确认请求 URL 正确
3. 验证请求方法和头部

## 部署流程

### 1. 准备部署

```bash
# 构建前端
npm run build

# 测试 Worker
wrangler dev --env production --remote
```

### 2. 部署到生产

```bash
# 部署 Worker
wrangler deploy

# 运行生产迁移
npm run db:migrate:prod

# 设置生产环境变量
wrangler secret put JWT_SECRET
wrangler secret put SENDGRID_API_KEY
```

### 3. 验证部署

```bash
# 测试生产 API
curl https://your-worker.your-subdomain.workers.dev/api/auth/send-verification \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'
```

## 监控和日志

### 开发环境

```bash
# 查看 Worker 日志
wrangler tail --env local

# 查看邮件日志
wrangler d1 execute ofun-email-db-local --local \
  --command "SELECT * FROM email_logs ORDER BY created_at DESC LIMIT 10"
```

### 生产环境

```bash
# 查看生产日志
wrangler tail

# 查看邮件统计
wrangler d1 execute ofun-email-db \
  --command "SELECT * FROM email_stats ORDER BY date DESC LIMIT 7"
```

## 开发工具

### 推荐的 VS Code 扩展

- Cloudflare Workers
- Vue Language Features (Volar)
- ESLint
- Prettier
- Thunder Client (API 测试)

### 有用的命令

```bash
# 查看 Worker 状态
wrangler whoami

# 列出所有 Workers
wrangler list

# 查看 D1 数据库
wrangler d1 list

# 查看环境变量
wrangler secret list
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

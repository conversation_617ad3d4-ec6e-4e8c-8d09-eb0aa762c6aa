#!/bin/bash

# Cloudflare 完整部署脚本
# 用于部署前端应用、邮件处理器和 API Worker

set -e

echo "🚀 开始部署 Cloudflare 应用..."

# 检查 wrangler 是否已安装
if ! command -v wrangler &> /dev/null; then
    echo "❌ 错误: wrangler 未安装"
    echo "请运行: npm install -g wrangler"
    exit 1
fi

# 检查是否已登录
if ! wrangler whoami &> /dev/null; then
    echo "❌ 错误: 未登录 Cloudflare"
    echo "请运行: wrangler login"
    exit 1
fi

# 构建前端应用
echo "🏗️  构建前端应用..."
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 未找到 package.json 文件"
    exit 1
fi

npm run build

echo "🌐 部署前端到 Cloudflare Pages..."
# 部署前端到 Cloudflare Pages
wrangler pages deploy dist --project-name tempmail-app

echo "🔗 部署统一 Worker..."
# 部署统一 Worker
wrangler deploy workers/unified.js --name ofun-email-unified --compatibility-date 2024-01-01

echo "✅ 所有应用部署完成!"

echo ""
echo "📋 部署信息:"
echo "- 前端应用: tempmail-app (Cloudflare Pages)"
echo "- 统一 Worker: ofun-email-unified (处理 API 和邮件)"
echo ""
echo "🌐 访问地址:"
echo "- 前端应用: https://tempmail-app-6o8.pages.dev"
echo "- API 端点: https://ofun-email-unified.your-subdomain.workers.dev"
echo ""
echo "🔧 接下来需要在 Cloudflare Dashboard 中配置："
echo ""
echo "Secrets (敏感信息):"
echo "- GITHUB_CLIENT_SECRET: c3a6bc53c55b1da20f11183bfc76b2641e11796b"
echo "- JWT_SECRET: 你的JWT密钥"
echo ""
echo "Variables (普通变量):"
echo "- GITHUB_CLIENT_ID: ********************"
echo "- GITHUB_REDIRECT_URI: https://tempmail.htmljs.cn/auth/github/callback"
echo "- ENVIRONMENT: production"
echo ""
echo "📧 邮件路由配置："
echo "1. 在 Cloudflare Dashboard 中配置 MX 记录"
echo "2. 设置 Catch-All 规则指向 ofun-email-unified"
echo ""
echo "🔗 GitHub OAuth 应用配置："
echo "Authorization callback URL: https://tempmail.htmljs.cn/auth/github/callback"
echo ""
echo "🎉 部署完成！"

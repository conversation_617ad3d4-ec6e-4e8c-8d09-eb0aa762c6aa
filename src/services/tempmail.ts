// 邮箱数据结构
export interface EmailData {
  id?: number; // 邮箱ID
  mail: string; // 邮件名
  domain: string; // 域名
  type?: string; // 类型（如：闲鱼、淘宝等）
  relation?: string; // 绑定关系（可选）
  remark?: string; // 备注（可选）
  secret?: string; // 授权密钥（可选，查询时不返回）
  createdAt: string; // 生成时间
  updatedAt: string; // 修改时间
  expire: string; // 过期时间
}

// 邮件项目结构
export interface MailItem {
  id: string;
  from: string;
  to: string;
  subject: string;
  text: string;
  html?: string;
  date: string;
  attachments?: any[];
}

// 授权密钥查询响应
export interface SecretQueryResponse {
  success: boolean;
  message?: string;
  emailData?: EmailData;
  expired?: boolean;
}

// 邮件列表响应
export interface MailListResponse {
  success: boolean;
  message?: string;
  emails?: MailItem[];
}

// 管理员登录响应
export interface AdminLoginResponse {
  success: boolean;
  message?: string;
  token?: string;
}

// 邮箱批量生成请求
export interface BatchGenerateRequest {
  count: number;
  domain: string;
  type: string;
  relation?: string;
  remark?: string;
  expireDays: number;
  prefix?: string;
}

// 邮箱批量生成响应
export interface BatchGenerateResponse {
  success: boolean;
  message?: string;
  emails?: EmailData[];
}

// 自定义邮箱生成请求
export interface CustomGenerateRequest {
  customName: string;
  domain: string;
  type: string;
  relation?: string;
  remark?: string;
  expireDays: number;
}

// 自定义邮箱生成响应
export interface CustomGenerateResponse {
  success: boolean;
  message?: string;
  email?: EmailData;
}

// 邮箱列表响应
export interface EmailListResponse {
  success: boolean;
  message?: string;
  emails?: EmailData[];
  total?: number;
}

export class EmailService {
  private apiUrl: string;
  private currentEmailData: EmailData | null = null;
  private currentSecret: string | null = null;

  constructor() {
    // 根据环境使用不同的 API URL
    this.apiUrl = import.meta.env.DEV
      ? "http://localhost:8787/api"
      : "https://ofun-email-unified.htmljs.workers.dev/api";
  }

  /**
   * 管理员登录
   */
  public async adminLogin(
    username: string,
    password: string
  ): Promise<AdminLoginResponse> {
    try {
      const response = await fetch(`${this.apiUrl}/admin/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          message:
            errorData.message || `HTTP error! status: ${response.status}`,
        };
      }

      const result: AdminLoginResponse = await response.json();
      return result;
    } catch (error) {
      console.error("Failed to login:", error);
      return {
        success: false,
        message: "登录失败，请检查网络连接",
      };
    }
  }

  /**
   * 获取邮箱列表（管理员）
   */
  public async getEmailList(
    token: string,
    page: number = 1,
    limit: number = 20
  ): Promise<EmailListResponse> {
    try {
      const response = await fetch(
        `${this.apiUrl}/admin/emails?page=${page}&limit=${limit}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          message:
            errorData.message || `HTTP error! status: ${response.status}`,
        };
      }

      const result: EmailListResponse = await response.json();
      return result;
    } catch (error) {
      console.error("Failed to get email list:", error);
      return {
        success: false,
        message: "获取邮箱列表失败，请检查网络连接",
      };
    }
  }

  /**
   * 批量生成邮箱（管理员）
   */
  public async batchGenerateEmails(
    token: string,
    request: BatchGenerateRequest
  ): Promise<BatchGenerateResponse> {
    try {
      const response = await fetch(`${this.apiUrl}/admin/emails/batch`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          message:
            errorData.message || `HTTP error! status: ${response.status}`,
        };
      }

      const result: BatchGenerateResponse = await response.json();
      return result;
    } catch (error) {
      console.error("Failed to batch generate emails:", error);
      return {
        success: false,
        message: "批量生成邮箱失败，请检查网络连接",
      };
    }
  }

  /**
   * 生成指定名称的邮箱（管理员）
   */
  public async customGenerateEmail(
    token: string,
    request: CustomGenerateRequest
  ): Promise<CustomGenerateResponse> {
    try {
      const response = await fetch(`${this.apiUrl}/admin/emails/custom`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          message:
            errorData.message || `HTTP error! status: ${response.status}`,
        };
      }

      const result: CustomGenerateResponse = await response.json();
      return result;
    } catch (error) {
      console.error("Failed to custom generate email:", error);
      return {
        success: false,
        message: "生成邮箱失败，请检查网络连接",
      };
    }
  }

  /**
   * 删除邮箱（管理员）
   */
  public async deleteEmail(
    token: string,
    emailId: number
  ): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await fetch(`${this.apiUrl}/admin/emails/${emailId}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          message:
            errorData.message || `HTTP error! status: ${response.status}`,
        };
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("Failed to delete email:", error);
      return {
        success: false,
        message: "删除邮箱失败，请检查网络连接",
      };
    }
  }

  /**
   * 批量删除邮箱（管理员）
   */
  public async batchDeleteEmails(
    token: string,
    emailIds: number[]
  ): Promise<{ success: boolean; message?: string; deletedCount?: number }> {
    try {
      const response = await fetch(`${this.apiUrl}/admin/emails/batch-delete`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ emailIds }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          message:
            errorData.message || `HTTP error! status: ${response.status}`,
        };
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("Failed to batch delete emails:", error);
      return {
        success: false,
        message: "批量删除邮箱失败，请检查网络连接",
      };
    }
  }

  /**
   * 更新邮箱信息（管理员）
   */
  public async updateEmail(
    token: string,
    emailId: number,
    updateData: {
      type?: string;
      relation?: string;
      remark?: string;
      expireDays?: number;
    }
  ): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await fetch(`${this.apiUrl}/admin/emails/${emailId}`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          message:
            errorData.message || `HTTP error! status: ${response.status}`,
        };
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("Failed to update email:", error);
      return {
        success: false,
        message: "更新邮箱失败，请检查网络连接",
      };
    }
  }

  /**
   * 通过授权密钥查询邮箱信息
   */
  public async queryBySecret(secret: string): Promise<SecretQueryResponse> {
    try {
      const response = await fetch(`${this.apiUrl}/email-query`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ secret }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          message:
            errorData.message || `HTTP error! status: ${response.status}`,
          expired: errorData.expired,
        };
      }

      const result: SecretQueryResponse = await response.json();

      if (result.success && result.emailData) {
        this.currentEmailData = result.emailData;
        this.currentSecret = secret; // 保存用户输入的 secret
      }

      return result;
    } catch (error) {
      console.error("Failed to query email by secret:", error);
      return {
        success: false,
        message: "查询失败，请检查网络连接",
      };
    }
  }

  /**
   * 获取当前邮箱的完整地址
   */
  public getCurrentEmailAddress(): string | null {
    if (!this.currentEmailData) {
      return null;
    }
    return `${this.currentEmailData.mail}@${this.currentEmailData.domain}`;
  }

  /**
   * 获取当前邮箱数据
   */
  public getCurrentEmailData(): EmailData | null {
    return this.currentEmailData;
  }

  /**
   * 获取当前邮箱的邮件列表
   */
  public async getMailList(): Promise<MailItem[]> {
    if (!this.currentEmailData || !this.currentSecret) {
      throw new Error("请先通过授权密钥查询邮箱");
    }

    try {
      const response = await fetch(`${this.apiUrl}/emails`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          secret: this.currentSecret,
          email: `${this.currentEmailData.mail}@${this.currentEmailData.domain}`,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const result: MailListResponse = await response.json();

      if (!result.success) {
        throw new Error(result.message || "获取邮件列表失败");
      }

      return result.emails || [];
    } catch (error) {
      console.error("Failed to get mail list:", error);
      throw error;
    }
  }

  /**
   * 获取邮件详情
   */
  public async getMailDetail(mailId: string): Promise<MailItem | null> {
    if (!this.currentEmailData || !this.currentSecret) {
      throw new Error("请先通过授权密钥查询邮箱");
    }

    try {
      const response = await fetch(`${this.apiUrl}/email-detail`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          secret: this.currentSecret,
          email: `${this.currentEmailData.mail}@${this.currentEmailData.domain}`,
          mailId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Failed to get mail detail:", errorData.message);
        return null;
      }

      const result = await response.json();

      if (!result.success) {
        return null;
      }

      return result.email || null;
    } catch (error) {
      console.error("Failed to get mail detail:", error);
      return null;
    }
  }

  /**
   * 批量删除邮件
   */
  public async batchDeleteMails(
    mailIds: string[]
  ): Promise<{ success: boolean; message?: string; deletedCount?: number }> {
    if (!this.currentEmailData || !this.currentSecret) {
      throw new Error("请先查询邮箱");
    }

    try {
      const response = await fetch(`${this.apiUrl}/emails/batch-delete`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          secret: this.currentSecret,
          mailIds: mailIds,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          message:
            errorData.message || `HTTP error! status: ${response.status}`,
        };
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("Failed to batch delete mails:", error);
      return {
        success: false,
        message: "批量删除邮件失败，请检查网络连接",
      };
    }
  }

  /**
   * 检查邮箱是否过期
   */
  public isEmailExpired(): boolean {
    if (!this.currentEmailData) {
      return true;
    }

    const expireTime = new Date(this.currentEmailData.expire);
    const now = new Date();
    return now > expireTime;
  }

  /**
   * 清除当前邮箱数据
   */
  public clearCurrentEmail(): void {
    this.currentEmailData = null;
    this.currentSecret = null;
  }

  /**
   * 提取验证码（保持原有功能）
   */
  public extractVerificationCode(text: string): string | null {
    // 提取6位数字验证码
    const codeMatch = text.match(/(?<![a-zA-Z@.])\b\d{6}\b/);
    return codeMatch ? codeMatch[0] : null;
  }

  /**
   * 获取邮件数量
   */
  public async getMailCount(): Promise<number> {
    if (!this.currentEmailData || !this.currentSecret) {
      throw new Error("请先查询邮箱");
    }

    try {
      const response = await fetch(`${this.apiUrl}/emails/count`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          secret: this.currentSecret,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || "获取邮件数量失败");
      }

      return result.count || 0;
    } catch (error) {
      console.error("Failed to get mail count:", error);
      throw error;
    }
  }

  /**
   * 获取最新验证码（保持原有功能）
   */
  public async getLatestVerificationCode(
    senderFilter?: string
  ): Promise<string | null> {
    try {
      const mails = await this.getMailList();

      if (mails.length === 0) {
        return null;
      }

      // 如果有发件人过滤，先过滤邮件
      const filteredMails = senderFilter
        ? mails.filter((mail) =>
            mail.from.toLowerCase().includes(senderFilter.toLowerCase())
          )
        : mails;

      if (filteredMails.length === 0) {
        return null;
      }

      // 获取最新邮件的详细内容
      const latestMail = filteredMails[0];
      const mailDetail = await this.getMailDetail(latestMail.id);

      if (!mailDetail) {
        return null;
      }

      // 从邮件文本中提取验证码
      return this.extractVerificationCode(mailDetail.text);
    } catch (error) {
      console.error("Failed to get verification code:", error);
      throw error;
    }
  }
}

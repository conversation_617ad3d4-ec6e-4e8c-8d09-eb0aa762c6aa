// 认证服务 - 前端 API 调用
import { useUserStore } from '@/stores/user'

const API_BASE = import.meta.env.DEV
  ? 'http://localhost:8787/api'
  : 'https://your-worker.your-subdomain.workers.dev/api'

// 类型定义
interface ApiResponse {
  success: boolean
  message?: string
  [key: string]: any
}

interface UserData {
  email: string
  password: string
  username?: string
  verificationCode?: string
}

interface LoginCredentials {
  email: string
  password: string
}

class AuthService {
  /**
   * 发送邮箱验证码
   * @param email 邮箱地址
   * @param purpose 用途（注册、重置密码等）
   */
  async sendVerificationCode(email: string, purpose: string = '注册'): Promise<ApiResponse> {
    try {
      const response = await fetch(`${API_BASE}/auth/send-verification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, purpose })
      })

      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.message || '发送验证码失败')
      }

      return data
    } catch (error) {
      console.error('发送验证码失败:', error)
      throw error
    }
  }

  /**
   * 验证邮箱验证码
   * @param email 邮箱地址
   * @param code 验证码
   */
  async verifyEmail(email: string, code: string): Promise<ApiResponse> {
    try {
      const response = await fetch(`${API_BASE}/auth/verify-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, code })
      })

      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.message || '验证失败')
      }

      return data
    } catch (error) {
      console.error('邮箱验证失败:', error)
      throw error
    }
  }

  /**
   * 用户注册
   * @param userData 用户数据
   */
  async register(userData: UserData): Promise<ApiResponse> {
    try {
      const response = await fetch(`${API_BASE}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
      })

      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.message || '注册失败')
      }

      return data
    } catch (error) {
      console.error('用户注册失败:', error)
      throw error
    }
  }

  /**
   * 用户登录
   * @param email 邮箱
   * @param password 密码
   */
  async login(email: string, password: string): Promise<ApiResponse> {
    try {
      const response = await fetch(`${API_BASE}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password })
      })

      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.message || '登录失败')
      }

      // 保存用户信息和令牌
      if (data.success && data.token) {
        const userStore = useUserStore()
        userStore.user = data.user
        userStore.token = data.token
        userStore.refreshToken = data.refreshToken
        userStore.credits = data.credits
      }

      return data
    } catch (error) {
      console.error('用户登录失败:', error)
      throw error
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      const token = localStorage.getItem('auth_token')
      
      if (token) {
        await fetch(`${API_BASE}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          }
        })
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清理本地存储
      const userStore = useUserStore()
      userStore.user = null
      userStore.token = null
      userStore.refreshToken = null
      userStore.credits = null
    }
  }

  /**
   * 获取用户信息
   */
  async getUserProfile(): Promise<ApiResponse> {
    try {
      const userStore = useUserStore()

      if (!userStore.token) {
        throw new Error('未登录')
      }

      const response = await fetch(`${API_BASE}/user/profile`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${userStore.token}`,
          'Content-Type': 'application/json',
        }
      })

      const data = await response.json()

      if (!response.ok) {
        if (response.status === 401) {
          // 令牌过期，清理登录状态
          this.logout()
        }
        throw new Error(data.message || '获取用户信息失败')
      }

      return data
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  /**
   * 检查认证状态
   */
  isAuthenticated(): boolean {
    const userStore = useUserStore()
    return userStore.isLoggedIn
  }

  /**
   * 获取认证令牌
   */
  getToken(): string | null {
    const userStore = useUserStore()
    return userStore.token
  }
}

// 创建单例实例
export const authService = new AuthService()

// 导出类以便测试
export { AuthService }

// 便捷函数
export const sendVerificationCode = (email: string, purpose?: string) =>
  authService.sendVerificationCode(email, purpose)

export const verifyEmail = (email: string, code: string) =>
  authService.verifyEmail(email, code)

export const register = (userData: UserData) =>
  authService.register(userData)

export const login = (email: string, password: string) =>
  authService.login(email, password)

export const logout = () =>
  authService.logout()

export const getUserProfile = () =>
  authService.getUserProfile()

export const isAuthenticated = () =>
  authService.isAuthenticated()

export const getToken = () =>
  authService.getToken()

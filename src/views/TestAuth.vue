<template>
  <div class="test-auth-page">
    <div class="container">
      <h1>🧪 邮件验证系统测试</h1>
      
      <!-- API 连接状态 -->
      <el-card class="status-card">
        <template #header>
          <h3>🔗 API 连接状态</h3>
        </template>
        <div class="status-info">
          <p><strong>前端地址:</strong> {{ frontendUrl }}</p>
          <p><strong>API 地址:</strong> {{ apiUrl }}</p>
          <p><strong>连接状态:</strong> 
            <el-tag :type="apiStatus === 'connected' ? 'success' : 'danger'">
              {{ apiStatus === 'connected' ? '✅ 已连接' : '❌ 未连接' }}
            </el-tag>
          </p>
          <el-button @click="testConnection" :loading="testing" type="primary" size="small">
            测试连接
          </el-button>
        </div>
      </el-card>

      <!-- 邮件验证测试 -->
      <el-card class="test-card">
        <template #header>
          <h3>📧 邮件验证测试</h3>
        </template>
        
        <el-form :model="testForm" label-width="100px">
          <el-form-item label="测试邮箱">
            <el-input 
              v-model="testForm.email" 
              placeholder="输入测试邮箱"
              :disabled="step > 1"
            />
          </el-form-item>
          
          <el-form-item v-if="step >= 2" label="验证码">
            <div class="code-input">
              <el-input 
                v-model="testForm.code" 
                placeholder="输入验证码"
                maxlength="6"
              />
              <el-button 
                @click="sendCode" 
                :loading="sending"
                :disabled="countdown > 0"
                type="text"
              >
                {{ countdown > 0 ? `${countdown}s后重发` : '重新发送' }}
              </el-button>
            </div>
          </el-form-item>
          
          <el-form-item v-if="step >= 3" label="密码">
            <el-input 
              v-model="testForm.password" 
              type="password"
              placeholder="输入密码"
            />
          </el-form-item>
          
          <el-form-item v-if="step >= 3" label="用户名">
            <el-input 
              v-model="testForm.username" 
              placeholder="输入用户名（可选）"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button 
              v-if="step === 1"
              @click="sendCode" 
              :loading="sending"
              type="primary"
            >
              发送验证码
            </el-button>
            
            <el-button 
              v-if="step === 2"
              @click="verifyCode" 
              :loading="verifying"
              type="primary"
            >
              验证邮箱
            </el-button>
            
            <el-button 
              v-if="step === 3"
              @click="registerUser" 
              :loading="registering"
              type="primary"
            >
              注册用户
            </el-button>
            
            <el-button 
              v-if="step > 1"
              @click="resetTest"
              type="default"
            >
              重新开始
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 测试日志 -->
      <el-card class="log-card">
        <template #header>
          <div class="log-header">
            <h3>📝 测试日志</h3>
            <el-button @click="clearLogs" size="small" type="text">清空日志</el-button>
          </div>
        </template>
        <div class="logs">
          <div 
            v-for="(log, index) in logs" 
            :key="index"
            :class="['log-item', log.type]"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <div v-if="logs.length === 0" class="no-logs">
            暂无日志
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { sendVerificationCode, verifyEmail, register } from '@/services/auth'

// 类型定义
interface LogItem {
  time: string
  message: string
  type: 'info' | 'success' | 'error'
}

interface TestForm {
  email: string
  code: string
  password: string
  username: string
}

// 响应式数据
const frontendUrl = ref(window.location.origin)
const apiUrl = ref(import.meta.env.DEV ? 'http://localhost:8787/api' : 'https://your-worker.your-subdomain.workers.dev/api')
const apiStatus = ref<'unknown' | 'connected' | 'error'>('unknown')
const testing = ref(false)

const step = ref(1) // 1: 发送验证码, 2: 验证邮箱, 3: 注册用户
const sending = ref(false)
const verifying = ref(false)
const registering = ref(false)
const countdown = ref(0)

const testForm = reactive<TestForm>({
  email: `test-${Date.now()}@ofun.my`,
  code: '',
  password: 'password123',
  username: 'testuser'
})

const logs = ref<LogItem[]>([])

// 添加日志
const addLog = (message: string, type: 'info' | 'success' | 'error' = 'info') => {
  logs.value.push({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
}

// 清空日志
const clearLogs = () => {
  logs.value = []
}

// 测试 API 连接
const testConnection = async () => {
  testing.value = true
  addLog('🔍 测试 API 连接...', 'info')
  
  try {
    const response = await fetch(`${apiUrl.value}/auth/send-verification`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: '<EMAIL>', purpose: '连接测试' })
    })
    
    if (response.ok) {
      apiStatus.value = 'connected'
      addLog('✅ API 连接成功', 'success')
      ElMessage.success('API 连接成功')
    } else {
      apiStatus.value = 'error'
      addLog(`❌ API 连接失败: ${response.status}`, 'error')
      ElMessage.error('API 连接失败')
    }
  } catch (error) {
    apiStatus.value = 'error'
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    addLog(`❌ API 连接错误: ${errorMessage}`, 'error')
    ElMessage.error('API 连接错误')
  } finally {
    testing.value = false
  }
}

// 发送验证码
const sendCode = async () => {
  if (!testForm.email) {
    ElMessage.error('请输入邮箱地址')
    return
  }
  
  sending.value = true
  addLog(`📧 发送验证码到: ${testForm.email}`, 'info')
  
  try {
    const result = await sendVerificationCode(testForm.email, '注册')
    addLog(`✅ 验证码发送成功: ${result.message}`, 'success')
    ElMessage.success('验证码发送成功，请查看控制台')
    step.value = 2
    startCountdown()
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    addLog(`❌ 验证码发送失败: ${errorMessage}`, 'error')
    ElMessage.error(errorMessage)
  } finally {
    sending.value = false
  }
}

// 验证邮箱
const verifyCode = async () => {
  if (!testForm.code) {
    ElMessage.error('请输入验证码')
    return
  }

  verifying.value = true
  addLog(`🔍 验证邮箱: ${testForm.email}, 验证码: ${testForm.code}`, 'info')

  try {
    const result = await verifyEmail(testForm.email, testForm.code)
    addLog(`✅ 邮箱验证成功: ${result.message}`, 'success')
    ElMessage.success('邮箱验证成功')
    step.value = 3
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    addLog(`❌ 邮箱验证失败: ${errorMessage}`, 'error')
    ElMessage.error(errorMessage)
  } finally {
    verifying.value = false
  }
}

// 注册用户
const registerUser = async () => {
  if (!testForm.password) {
    ElMessage.error('请输入密码')
    return
  }

  registering.value = true
  addLog(`👤 注册用户: ${testForm.email}`, 'info')

  try {
    const result = await register({
      email: testForm.email,
      password: testForm.password,
      username: testForm.username,
      verificationCode: testForm.code
    })
    addLog(`✅ 用户注册成功: ${result.message}`, 'success')
    ElMessage.success('用户注册成功！')
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    addLog(`❌ 用户注册失败: ${errorMessage}`, 'error')
    ElMessage.error(errorMessage)
  } finally {
    registering.value = false
  }
}

// 重置测试
const resetTest = () => {
  step.value = 1
  testForm.email = `test-${Date.now()}@ofun.my`
  testForm.code = ''
  testForm.password = 'password123'
  testForm.username = 'testuser'
  countdown.value = 0
  addLog('🔄 重置测试', 'info')
}

// 倒计时
const startCountdown = () => {
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// 页面加载时测试连接
onMounted(() => {
  testConnection()
})
</script>

<style scoped>
.test-auth-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.container h1 {
  text-align: center;
  margin-bottom: 30px;
  color: #303133;
}

.status-card,
.test-card,
.log-card {
  margin-bottom: 20px;
}

.status-info p {
  margin: 8px 0;
}

.code-input {
  display: flex;
  gap: 10px;
  align-items: center;
}

.code-input .el-input {
  flex: 1;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logs {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  display: flex;
  margin-bottom: 8px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #909399;
  margin-right: 10px;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-item.success .log-message {
  color: #67c23a;
}

.log-item.error .log-message {
  color: #f56c6c;
}

.log-item.info .log-message {
  color: #409eff;
}

.no-logs {
  text-align: center;
  color: #909399;
  font-style: italic;
}
</style>

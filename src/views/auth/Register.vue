<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center py-12 px-4">
    <div class="max-w-md w-full space-y-8">
      <!-- 头部 -->
      <div class="text-center">
        <router-link to="/" class="inline-flex items-center space-x-2 text-2xl font-bold text-gray-800 mb-8">
          <i class="fas fa-envelope text-blue-600"></i>
          <span>TempMail</span>
        </router-link>
        <h2 class="text-3xl font-bold text-gray-900 mb-2">
          创建账户
        </h2>
        <p class="text-gray-600">
          注册即可获得5个免费积分
        </p>
      </div>

      <!-- 注册表单 -->
      <div class="bg-white rounded-xl shadow-lg p-8">
        <!-- 第一步：输入邮箱 -->
        <div v-if="!verificationSent">
          <el-form
            ref="emailFormRef"
            :model="registerForm"
            :rules="emailRules"
            @submit.prevent="sendVerificationCode"
            label-position="top"
            class="space-y-6"
          >
            <el-form-item label="邮箱地址" prop="email">
              <el-input
                v-model="registerForm.email"
                type="email"
                placeholder="请输入您的邮箱地址"
                size="large"
                :prefix-icon="Message"
                :disabled="sendingCode"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :loading="sendingCode"
                @click="sendVerificationCode"
                class="w-full"
              >
                <i class="fas fa-paper-plane mr-2"></i>
                发送验证码
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 第二步：完整注册表单 -->
        <div v-else>
          <el-form
            ref="registerFormRef"
            :model="registerForm"
            :rules="registerRules"
            @submit.prevent="handleRegister"
            label-position="top"
            class="space-y-6"
          >
            <!-- 邮箱显示（只读） -->
            <el-form-item label="邮箱地址">
              <el-input
                :model-value="registerForm.email"
                size="large"
                :prefix-icon="Message"
                readonly
                class="bg-gray-50"
              />
            </el-form-item>

            <!-- 验证码输入 -->
            <el-form-item label="邮箱验证码" prop="verificationCode">
              <div class="flex space-x-2">
                <el-input
                  v-model="registerForm.verificationCode"
                  placeholder="请输入6位验证码"
                  size="large"
                  :prefix-icon="Message"
                  maxlength="6"
                  class="flex-1"
                />
                <el-button
                  :disabled="countdown > 0 || sendingCode"
                  :loading="sendingCode"
                  @click="resendVerificationCode"
                  size="large"
                >
                  {{ countdown > 0 ? `${countdown}s` : '重新发送' }}
                </el-button>
              </div>
              <p class="text-sm text-gray-500 mt-1">
                验证码已发送到您的邮箱，请查收（有效期10分钟）
              </p>
            </el-form-item>

            <!-- 用户名 -->
            <el-form-item label="用户名（可选）" prop="username">
              <el-input
                v-model="registerForm.username"
                placeholder="请输入用户名"
                size="large"
                :prefix-icon="User"
              />
            </el-form-item>

            <!-- 密码 -->
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="registerForm.password"
                type="password"
                placeholder="请输入密码（至少6位）"
                size="large"
                :prefix-icon="Lock"
                show-password
              />
            </el-form-item>

            <!-- 确认密码 -->
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="registerForm.confirmPassword"
                type="password"
                placeholder="请再次输入密码"
                size="large"
                :prefix-icon="Lock"
                show-password
              />
            </el-form-item>

            <el-form-item>
              <div class="space-y-3">
                <el-button
                  type="primary"
                  size="large"
                  :loading="userStore.loading"
                  @click="handleRegister"
                  class="w-full"
                >
                  <i class="fas fa-user-plus mr-2"></i>
                  完成注册
                </el-button>

                <el-button
                  size="large"
                  @click="resetForm"
                  class="w-full"
                >
                  <i class="fas fa-arrow-left mr-2"></i>
                  重新输入邮箱
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 分割线 -->
        <div class="relative my-6">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white text-gray-500">或</span>
          </div>
        </div>

        <!-- GitHub 注册 -->
        <el-button
          size="large"
          :loading="githubLoading"
          @click="handleGitHubRegister"
          class="w-full"
          style="background-color: #24292e; border-color: #24292e; color: white;"
        >
          <i class="fab fa-github mr-2"></i>
          使用 GitHub 注册
        </el-button>

        <!-- 底部链接 -->
        <div class="mt-6 text-center space-y-2">
          <p class="text-sm text-gray-600">
            已有账户？
            <router-link to="/login" class="text-blue-600 hover:text-blue-700 font-medium">
              立即登录
            </router-link>
          </p>
          <router-link to="/" class="text-sm text-gray-500 hover:text-gray-700">
            返回首页
          </router-link>
        </div>

        <!-- 注册福利提示 -->
        <div class="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
          <div class="flex items-center space-x-2 text-green-800">
            <i class="fas fa-gift text-green-600"></i>
            <div class="text-sm">
              <p class="font-medium">新用户福利</p>
              <p>注册即可获得5个免费积分，每个积分可生成一个临时邮箱</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElForm } from 'element-plus'
import { Message, Lock, User } from '@element-plus/icons-vue'
import { useUserStore } from '../../stores/user'

const router = useRouter()
const userStore = useUserStore()

// 表单引用
const emailFormRef = ref<InstanceType<typeof ElForm>>()
const registerFormRef = ref<InstanceType<typeof ElForm>>()

// 表单数据
const registerForm = reactive({
  email: '',
  username: '',
  password: '',
  confirmPassword: '',
  verificationCode: ''
})

// 验证码相关状态
const verificationSent = ref(false)
const sendingCode = ref(false)
const countdown = ref(0)
const countdownTimer = ref<number | null>(null)

// 自定义验证器
const validateConfirmPassword = (_rule: any, value: string, callback: Function) => {
  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 邮箱验证规则（第一步）
const emailRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email' as const, message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 完整表单验证规则（第二步）
const registerRules = {
  username: [
    { min: 3, max: 20, message: '用户名长度应在3-20个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  verificationCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码为6位数字', trigger: 'blur' }
  ]
}

// GitHub 注册加载状态
const githubLoading = ref(false)

// 发送验证码
const sendVerificationCode = async () => {
  if (!emailFormRef.value) return

  try {
    // 表单验证，如果验证失败会抛出异常
    await emailFormRef.value.validate()
  } catch (error) {
    // 表单验证失败，直接返回，不显示错误（Element Plus 会自动显示验证错误）
    return
  }

  sendingCode.value = true
  try {
    // 发送验证码
    const apiUrl = import.meta.env.DEV
      ? 'http://localhost:8787/api'
      : 'https://ofun-email-system.htmljs.workers.dev/api'
    const response = await fetch(`${apiUrl}/auth/send-verification`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: registerForm.email,
        purpose: '注册'
      })
    })

    const result = await response.json()

    if (result.success) {
      ElMessage.success('验证码已发送到您的邮箱，请查收')
      verificationSent.value = true
      startCountdown()
    } else {
      const errorMessage = result.message || '发送验证码失败'
      ElMessage.error(errorMessage)
      console.error('Send verification failed:', result)
    }
  } catch (error) {
    console.error('Send verification code error:', error)
    const errorMessage = error instanceof Error ? error.message : '网络连接失败'
    ElMessage.error(errorMessage.includes('fetch') ? '网络连接失败，请检查网络' : errorMessage)
  } finally {
    sendingCode.value = false
  }
}

// 重新发送验证码
const resendVerificationCode = async () => {
  await sendVerificationCode()
}

// 重置表单
const resetForm = () => {
  verificationSent.value = false
  registerForm.email = ''
  registerForm.username = ''
  registerForm.password = ''
  registerForm.confirmPassword = ''
  registerForm.verificationCode = ''
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
    countdown.value = 0
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  countdownTimer.value = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      if (countdownTimer.value) {
        clearInterval(countdownTimer.value)
        countdownTimer.value = null
      }
    }
  }, 1000) as unknown as number
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    // 表单验证，如果验证失败会抛出异常
    await registerFormRef.value.validate()
  } catch (error) {
    // 表单验证失败，直接返回，不显示错误（Element Plus 会自动显示验证错误）
    return
  }

  try {
    // 提交完整的注册信息
    const result = await userStore.register({
      email: registerForm.email,
      username: registerForm.username || undefined,
      password: registerForm.password,
      verificationCode: registerForm.verificationCode
    })

    if (result.success) {
      ElMessage.success('注册成功！请登录您的账户')
      router.push('/login')
    } else {
      // 显示具体的错误信息
      const errorMessage = result.message || '注册失败，请稍后重试'
      ElMessage.error(errorMessage)
      console.error('Registration failed:', result)
    }
  } catch (error) {
    console.error('Register error:', error)
    const errorMessage = error instanceof Error ? error.message : '注册过程中发生未知错误'
    ElMessage.error(errorMessage)
  }
}

// 处理 GitHub 注册
const handleGitHubRegister = async () => {
  githubLoading.value = true
  try {
    // 获取 GitHub OAuth URL
    const response = await fetch('https://ofun-email-system.htmljs.workers.dev/api/auth/github')
    const result = await response.json()

    if (result.success && result.authUrl) {
      // 保存 state 到 localStorage
      localStorage.setItem('github-oauth-state', result.state)
      
      // 重定向到 GitHub
      window.location.href = result.authUrl
    } else {
      ElMessage.error('GitHub 注册初始化失败')
    }
  } catch (error) {
    console.error('GitHub register error:', error)
    ElMessage.error('GitHub 注册失败')
  } finally {
    githubLoading.value = false
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
})
</script>

<style scoped>
.el-form-item {
  margin-bottom: 24px;
}

.el-input {
  --el-input-height: 48px;
}

.el-button {
  height: 48px;
  font-size: 16px;
}
</style>

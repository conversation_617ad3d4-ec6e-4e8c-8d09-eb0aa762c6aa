<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center py-12 px-4">
    <div class="max-w-md w-full space-y-8">
      <div class="text-center">
        <router-link to="/" class="inline-flex items-center space-x-2 text-2xl font-bold text-gray-800 mb-8">
          <i class="fas fa-envelope text-blue-600"></i>
          <span>TempMail</span>
        </router-link>
        
        <div v-if="loading" class="space-y-4">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <h2 class="text-2xl font-bold text-gray-900">
            正在处理 GitHub 登录...
          </h2>
          <p class="text-gray-600">
            请稍候，我们正在验证您的身份
          </p>
        </div>

        <div v-else-if="error" class="space-y-4">
          <div class="text-red-500 text-6xl">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <h2 class="text-2xl font-bold text-gray-900">
            登录失败
          </h2>
          <p class="text-gray-600">
            {{ error }}
          </p>
          <div class="space-y-2">
            <router-link 
              to="/login" 
              class="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              返回登录页面
            </router-link>
            <br>
            <router-link 
              to="/" 
              class="inline-block text-gray-500 hover:text-gray-700 transition-colors"
            >
              返回首页
            </router-link>
          </div>
        </div>

        <div v-else class="space-y-4">
          <div class="text-green-500 text-6xl">
            <i class="fas fa-check-circle"></i>
          </div>
          <h2 class="text-2xl font-bold text-gray-900">
            登录成功！
          </h2>
          <p class="text-gray-600">
            正在跳转到首页...
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '../../stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const loading = ref(true)
const error = ref('')

onMounted(async () => {
  try {
    // 获取 URL 参数
    const code = route.query.code as string
    const state = route.query.state as string
    const storedState = localStorage.getItem('github-oauth-state')

    // 验证 state 参数
    if (!code || !state || state !== storedState) {
      throw new Error('无效的授权参数')
    }

    // 调用后端处理 GitHub 回调
    const apiUrl = import.meta.env.DEV
      ? 'http://localhost:8787/api'
      : 'https://ofun-email-system.htmljs.workers.dev/api'
    const response = await fetch(`${apiUrl}/auth/github/callback`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ code, state })
    })

    const result = await response.json()

    if (result.success && result.token) {
      // 保存令牌和用户信息
      userStore.token = result.token
      userStore.user = result.user
      userStore.refreshToken = result.refreshToken

      // 清除 state
      localStorage.removeItem('github-oauth-state')

      ElMessage.success('GitHub 登录成功！')

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        const redirect = route.query.redirect as string || '/'
        router.push(redirect)
      }, 1500)
    } else {
      throw new Error(result.message || 'GitHub 登录失败')
    }
  } catch (err) {
    console.error('GitHub callback error:', err)
    error.value = err instanceof Error ? err.message : 'GitHub 登录处理失败'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>

<template>
  <div class="space-y-6">
    <!-- 头部 -->
    <div class="flex items-center justify-between">
      <h2 class="text-xl font-semibold text-gray-800">兑换码管理</h2>
      <div class="flex items-center space-x-4">
        <el-button type="primary" @click="showGenerateDialog">
          <i class="fas fa-plus mr-1"></i>
          批量生成
        </el-button>
        <el-button @click="refreshCodes" :loading="loading">
          <i class="fas fa-refresh mr-1"></i>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="bg-blue-100 p-3 rounded-full">
            <i class="fas fa-ticket-alt text-blue-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-600">总兑换码</p>
            <p class="text-2xl font-semibold text-gray-800">{{ stats.total }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="bg-green-100 p-3 rounded-full">
            <i class="fas fa-check-circle text-green-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-600">已使用</p>
            <p class="text-2xl font-semibold text-gray-800">{{ stats.used }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="bg-yellow-100 p-3 rounded-full">
            <i class="fas fa-clock text-yellow-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-600">可用</p>
            <p class="text-2xl font-semibold text-gray-800">{{ stats.available }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="bg-red-100 p-3 rounded-full">
            <i class="fas fa-times-circle text-red-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-600">已过期</p>
            <p class="text-2xl font-semibold text-gray-800">{{ stats.expired }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 兑换码列表 -->
    <div class="bg-white rounded-lg shadow">
      <div class="p-6">
        <el-table
          :data="codes"
          v-loading="loading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          
          <el-table-column prop="code" label="兑换码" width="150">
            <template #default="{ row }">
              <div class="flex items-center space-x-2">
                <code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                  {{ row.code }}
                </code>
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="copyCode(row.code)"
                >
                  <i class="fas fa-copy"></i>
                </el-button>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="credits" label="积分" width="80">
            <template #default="{ row }">
              <span class="font-semibold text-blue-600">{{ row.credits }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="description" label="描述" min-width="150" />
          
          <el-table-column label="使用情况" width="120">
            <template #default="{ row }">
              <div class="text-center">
                <p class="text-sm">
                  <span :class="row.used_count >= row.max_uses ? 'text-red-600' : 'text-green-600'">
                    {{ row.used_count }}
                  </span>
                  / {{ row.max_uses }}
                </p>
                <el-progress
                  :percentage="(row.used_count / row.max_uses) * 100"
                  :stroke-width="4"
                  :show-text="false"
                  :status="row.used_count >= row.max_uses ? 'exception' : 'success'"
                />
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="getStatusType(row)"
                size="small"
              >
                {{ getStatusText(row) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="expires_at" label="过期时间" width="160">
            <template #default="{ row }">
              {{ row.expires_at ? formatDate(row.expires_at) : '永不过期' }}
            </template>
          </el-table-column>
          
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button
                size="small"
                :type="row.is_active ? 'warning' : 'success'"
                @click="toggleCodeStatus(row)"
              >
                {{ row.is_active ? '禁用' : '启用' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="flex justify-center mt-6">
          <el-pagination
            v-model:current-page="pagination.page"
            :page-size="pagination.limit"
            :total="pagination.total"
            layout="total, prev, pager, next, jumper"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- 批量生成对话框 -->
    <el-dialog
      v-model="generateDialog.visible"
      title="批量生成兑换码"
      width="500px"
    >
      <el-form :model="generateDialog.form" label-width="100px">
        <el-form-item label="生成数量" required>
          <el-input-number
            v-model="generateDialog.form.count"
            :min="1"
            :max="100"
            placeholder="最多100个"
            class="w-full"
          />
        </el-form-item>
        
        <el-form-item label="积分数量" required>
          <el-input-number
            v-model="generateDialog.form.credits"
            :min="1"
            :max="10000"
            placeholder="每个兑换码的积分"
            class="w-full"
          />
        </el-form-item>
        
        <el-form-item label="描述" required>
          <el-input
            v-model="generateDialog.form.description"
            placeholder="兑换码用途描述"
          />
        </el-form-item>
        
        <el-form-item label="使用次数">
          <el-input-number
            v-model="generateDialog.form.maxUses"
            :min="1"
            :max="1000"
            placeholder="每个兑换码可使用次数"
            class="w-full"
          />
        </el-form-item>
        
        <el-form-item label="有效期">
          <el-input-number
            v-model="generateDialog.form.expiresInDays"
            :min="1"
            :max="365"
            placeholder="天数，留空为永不过期"
            class="w-full"
          />
          <p class="text-xs text-gray-500 mt-1">留空表示永不过期</p>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="generateDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="generateDialog.loading"
          @click="confirmGenerate"
        >
          生成兑换码
        </el-button>
      </template>
    </el-dialog>

    <!-- 生成结果对话框 -->
    <el-dialog
      v-model="resultDialog.visible"
      title="生成结果"
      width="600px"
    >
      <div class="space-y-4">
        <p class="text-green-600 font-medium">
          <i class="fas fa-check-circle mr-2"></i>
          成功生成 {{ resultDialog.codes.length }} 个兑换码
        </p>
        
        <div class="bg-gray-50 rounded-lg p-4 max-h-60 overflow-y-auto">
          <div v-for="code in resultDialog.codes" :key="code.id" class="flex items-center justify-between py-2 border-b border-gray-200 last:border-b-0">
            <code class="bg-white px-2 py-1 rounded text-sm font-mono">{{ code.code }}</code>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-600">{{ code.credits }} 积分</span>
              <el-button size="small" type="primary" text @click="copyCode(code.code)">
                <i class="fas fa-copy"></i>
              </el-button>
            </div>
          </div>
        </div>
        
        <div class="flex justify-center">
          <el-button type="primary" @click="copyAllCodes">
            <i class="fas fa-copy mr-2"></i>
            复制所有兑换码
          </el-button>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="resultDialog.visible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useClipboard } from '@vueuse/core'
import { formatDateToChinaTime } from '../../../utils/dateFormat'

const props = defineProps<{
  authToken: string
}>()

const { copy } = useClipboard()

// 响应式数据
const loading = ref(false)
const codes = ref<any[]>([])

const pagination = ref({
  page: 1,
  limit: 20,
  total: 0
})

const generateDialog = ref({
  visible: false,
  loading: false,
  form: {
    count: 10,
    credits: 10,
    description: '',
    maxUses: 1,
    expiresInDays: null as number | null
  }
})

const resultDialog = ref({
  visible: false,
  codes: [] as any[]
})

// 计算属性
const stats = computed(() => {
  const now = new Date()
  return {
    total: codes.value.length,
    used: codes.value.filter(c => c.used_count >= c.max_uses).length,
    available: codes.value.filter(c => 
      c.is_active && 
      c.used_count < c.max_uses && 
      (!c.expires_at || new Date(c.expires_at) > now)
    ).length,
    expired: codes.value.filter(c => 
      c.expires_at && new Date(c.expires_at) <= now
    ).length
  }
})

// API 基础 URL
const apiUrl = import.meta.env.DEV
  ? 'http://localhost:8787/api'
  : 'https://ofun-email-system.htmljs.workers.dev/api'

// 获取认证头
const getAuthHeaders = () => ({
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${props.authToken}`
})

// 格式化日期
const formatDate = (dateStr: string) => {
  return formatDateToChinaTime(dateStr, 'YYYY-MM-DD HH:mm')
}

// 获取状态类型
const getStatusType = (row: any) => {
  if (!row.is_active) return 'info'
  if (row.used_count >= row.max_uses) return 'danger'
  if (row.expires_at && new Date(row.expires_at) <= new Date()) return 'warning'
  return 'success'
}

// 获取状态文本
const getStatusText = (row: any) => {
  if (!row.is_active) return '已禁用'
  if (row.used_count >= row.max_uses) return '已用完'
  if (row.expires_at && new Date(row.expires_at) <= new Date()) return '已过期'
  return '可用'
}

// 获取兑换码列表
const fetchCodes = async (page = 1) => {
  loading.value = true
  try {
    const response = await fetch(`${apiUrl}/admin/redemption-codes?page=${page}&limit=${pagination.value.limit}`, {
      headers: getAuthHeaders()
    })

    const result = await response.json()
    
    if (result.success) {
      codes.value = result.codes || []
      pagination.value = { ...pagination.value, ...result.pagination }
    } else {
      ElMessage.error(result.message || '获取兑换码列表失败')
    }
  } catch (error) {
    console.error('Fetch codes error:', error)
    ElMessage.error('获取兑换码列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新兑换码列表
const refreshCodes = () => {
  fetchCodes(pagination.value.page)
}

// 处理分页变化
const handlePageChange = (page: number) => {
  fetchCodes(page)
}

// 显示生成对话框
const showGenerateDialog = () => {
  generateDialog.value.form = {
    count: 10,
    credits: 10,
    description: '',
    maxUses: 1,
    expiresInDays: null
  }
  generateDialog.value.visible = true
}

// 确认生成
const confirmGenerate = async () => {
  const form = generateDialog.value.form
  
  if (!form.count || !form.credits || !form.description) {
    ElMessage.warning('请填写完整信息')
    return
  }

  generateDialog.value.loading = true
  try {
    const response = await fetch(`${apiUrl}/admin/redemption-codes/generate`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(form)
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success(result.message)
      generateDialog.value.visible = false
      resultDialog.value.codes = result.codes || []
      resultDialog.value.visible = true
      refreshCodes()
    } else {
      ElMessage.error(result.message || '生成兑换码失败')
    }
  } catch (error) {
    console.error('Generate codes error:', error)
    ElMessage.error('生成兑换码失败')
  } finally {
    generateDialog.value.loading = false
  }
}

// 复制兑换码
const copyCode = (code: string) => {
  copy(code)
  ElMessage.success('兑换码已复制到剪贴板')
}

// 复制所有兑换码
const copyAllCodes = () => {
  const allCodes = resultDialog.value.codes.map(c => c.code).join('\n')
  copy(allCodes)
  ElMessage.success('所有兑换码已复制到剪贴板')
}

// 切换兑换码状态
const toggleCodeStatus = async (code: any) => {
  const newStatus = !code.is_active
  const action = newStatus ? '启用' : '禁用'
  
  try {
    await ElMessageBox.confirm(`确定要${action}兑换码 ${code.code} 吗？`, `${action}兑换码`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await fetch(`${apiUrl}/admin/redemption-codes`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        id: code.id,
        isActive: newStatus
      })
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success(`兑换码${action}成功`)
      refreshCodes()
    } else {
      ElMessage.error(result.message || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Toggle code status error:', error)
      ElMessage.error(`${action}失败`)
    }
  }
}

// 组件挂载时
onMounted(() => {
  fetchCodes()
})
</script>

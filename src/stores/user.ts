import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 用户信息接口
export interface User {
  id: number
  email: string
  username?: string
  displayName?: string
  avatarUrl?: string
  emailVerified: boolean
  createdAt: string
  lastLoginAt?: string
}

// 配额信息接口
export interface Credits {
  balance: number
  totalEarned: number
  totalSpent: number
}

// 交易记录接口
export interface Transaction {
  id: number
  type: 'earn' | 'spend' | 'refund' | 'admin_adjust'
  amount: number
  balanceAfter: number
  description: string
  referenceType?: string
  referenceId?: string
  createdAt: string
}

// API 响应接口
interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const credits = ref<Credits | null>(null)
  const token = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const loading = ref(false)

  // API 基础 URL
  const apiUrl = import.meta.env.DEV
    ? 'http://localhost:8787/api'
    : 'https://ofun-email-system.htmljs.workers.dev/api'

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)

  // 设置认证头
  const getAuthHeaders = () => ({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token.value}`
  })

  // 处理 API 错误
  const handleApiError = (error: any) => {
    console.error('API Error:', error)
    if (error.status === 401) {
      logout()
    }
    throw error
  }

  // 用户注册
  const register = async (userData: {
    email: string
    password: string
    username?: string
    verificationCode?: string
  }): Promise<ApiResponse> => {
    loading.value = true
    try {
      const response = await fetch(`${apiUrl}/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData)
      })

      const result = await response.json()

      if (!response.ok) {
        // 返回具体的错误信息而不是抛出异常
        return {
          success: false,
          message: result.message || `注册失败 (${response.status})`
        }
      }

      return result
    } catch (error) {
      console.error('Register API error:', error)
      const errorMessage = error instanceof Error ? error.message : '网络连接失败'
      return {
        success: false,
        message: errorMessage.includes('fetch') ? '网络连接失败，请检查网络' : errorMessage
      }
    } finally {
      loading.value = false
    }
  }

  // 用户登录
  const login = async (credentials: {
    email: string
    password: string
  }): Promise<ApiResponse> => {
    loading.value = true
    try {
      const response = await fetch(`${apiUrl}/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials)
      })

      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.message || '登录失败')
      }

      if (result.success && result.token) {
        // 保存令牌
        token.value = result.token
        refreshToken.value = result.refreshToken

        // 保存用户信息
        user.value = result.user
      }

      return result
    } catch (error) {
      handleApiError(error)
      return { success: false, message: '登录失败，请稍后重试' }
    } finally {
      loading.value = false
    }
  }

  // 用户登出
  const logout = async () => {
    try {
      if (token.value) {
        await fetch(`${apiUrl}/auth/logout`, {
          method: 'POST',
          headers: getAuthHeaders()
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除本地状态
      user.value = null
      credits.value = null
      token.value = null
      refreshToken.value = null
    }
  }

  // 刷新令牌
  const refreshAccessToken = async (): Promise<boolean> => {
    if (!refreshToken.value) return false

    try {
      const response = await fetch(`${apiUrl}/auth/refresh`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken: refreshToken.value })
      })

      const result = await response.json()

      if (result.success && result.token) {
        token.value = result.token
        refreshToken.value = result.refreshToken
        return true
      }
    } catch (error) {
      console.error('Refresh token error:', error)
    }

    // 刷新失败，清除状态
    logout()
    return false
  }

  // 获取用户资料
  const fetchProfile = async (): Promise<ApiResponse> => {
    if (!token.value) return { success: false, message: '未登录' }

    loading.value = true
    try {
      const response = await fetch(`${apiUrl}/user/profile`, {
        headers: getAuthHeaders()
      })

      if (response.status === 401) {
        // 尝试刷新令牌
        const refreshed = await refreshAccessToken()
        if (refreshed) {
          return fetchProfile() // 重试
        }
        throw new Error('认证失败')
      }

      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.message || '获取用户信息失败')
      }

      if (result.success) {
        user.value = result.user
        credits.value = result.credits
      }

      return result
    } catch (error) {
      handleApiError(error)
      return { success: false, message: '获取用户信息失败' }
    } finally {
      loading.value = false
    }
  }

  // 获取交易记录
  const fetchTransactions = async (page = 1, limit = 20): Promise<ApiResponse> => {
    if (!token.value) return { success: false, message: '未登录' }

    try {
      const response = await fetch(`${apiUrl}/user/transactions?page=${page}&limit=${limit}`, {
        headers: getAuthHeaders()
      })

      if (response.status === 401) {
        const refreshed = await refreshAccessToken()
        if (refreshed) {
          return fetchTransactions(page, limit)
        }
        throw new Error('认证失败')
      }

      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.message || '获取交易记录失败')
      }

      return result
    } catch (error) {
      handleApiError(error)
      return { success: false, message: '获取交易记录失败' }
    }
  }

  // 生成临时邮箱
  const generateEmail = async (params: {
    domain: string
    customName?: string
  }): Promise<ApiResponse> => {
    if (!token.value) return { success: false, message: '未登录' }

    loading.value = true
    try {
      const response = await fetch(`${apiUrl}/user/generate-email`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(params)
      })

      if (response.status === 401) {
        const refreshed = await refreshAccessToken()
        if (refreshed) {
          return generateEmail(params)
        }
        throw new Error('认证失败')
      }

      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.message || '生成邮箱失败')
      }

      if (result.success) {
        // 更新配额余额
        if (credits.value) {
          credits.value.balance = result.creditsRemaining
          credits.value.totalSpent += 1
        }
      }

      return result
    } catch (error) {
      handleApiError(error)
      return { success: false, message: '生成邮箱失败' }
    } finally {
      loading.value = false
    }
  }

  // 兑换配额码
  const redeemCode = async (code: string): Promise<ApiResponse> => {
    if (!token.value) return { success: false, message: '未登录' }

    loading.value = true
    try {
      const response = await fetch(`${apiUrl}/redemption/redeem`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ code })
      })

      if (response.status === 401) {
        const refreshed = await refreshAccessToken()
        if (refreshed) {
          return redeemCode(code)
        }
        throw new Error('认证失败')
      }

      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.message || '兑换失败')
      }

      if (result.success) {
        // 更新配额余额
        if (credits.value) {
          credits.value.balance = result.newBalance
          credits.value.totalEarned += result.creditsAwarded
        }
      }

      return result
    } catch (error) {
      handleApiError(error)
      return { success: false, message: '兑换失败' }
    } finally {
      loading.value = false
    }
  }

  // 初始化用户状态
  const initializeUser = async () => {
    if (token.value) {
      await fetchProfile()
    }
  }

  return {
    // 状态
    user,
    credits,
    token,
    refreshToken,
    loading,
    
    // 计算属性
    isLoggedIn,
    
    // 方法
    register,
    login,
    logout,
    fetchProfile,
    fetchTransactions,
    generateEmail,
    redeemCode,
    initializeUser
  }
}, {
  persist: {
    key: 'user-store',
    storage: localStorage,
    paths: ['user', 'credits', 'token', 'refreshToken']
  }
})

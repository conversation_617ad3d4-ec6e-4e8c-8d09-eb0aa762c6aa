<template>
  <div class="register-form">
    <el-card class="register-card">
      <template #header>
        <div class="card-header">
          <h2>用户注册</h2>
          <p>创建您的 ofun.my 账户</p>
        </div>
      </template>

      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        label-width="80px"
        @submit.prevent="handleRegister"
      >
        <!-- 邮箱输入 -->
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="registerForm.email"
            type="email"
            placeholder="请输入邮箱地址"
            :disabled="step > 1"
          >
            <template #prefix>
              <el-icon><Message /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <!-- 验证码步骤 -->
        <div v-if="step >= 2" class="verification-step">
          <el-form-item label="验证码" prop="verificationCode">
            <div class="verification-input">
              <el-input
                v-model="registerForm.verificationCode"
                placeholder="请输入6位验证码"
                maxlength="6"
                :disabled="verifying"
              >
                <template #prefix>
                  <el-icon><Key /></el-icon>
                </template>
              </el-input>
              <el-button
                type="text"
                :disabled="countdown > 0 || sendingCode"
                @click="sendVerificationCode"
                class="resend-btn"
              >
                {{ countdown > 0 ? `${countdown}s后重发` : '重新发送' }}
              </el-button>
            </div>
          </el-form-item>

          <el-alert
            title="验证码已发送到您的邮箱"
            type="info"
            :closable="false"
            show-icon
            class="verification-alert"
          >
            <template #default>
              <p>请查收邮件并输入6位验证码</p>
              <p class="text-sm">验证码有效期为10分钟</p>
            </template>
          </el-alert>
        </div>

        <!-- 用户信息步骤 -->
        <div v-if="step >= 3" class="user-info-step">
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="registerForm.username"
              placeholder="请输入用户名（可选）"
            >
              <template #prefix>
                <el-icon><User /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="密码" prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="请输入密码（至少6位）"
              show-password
            >
              <template #prefix>
                <el-icon><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              show-password
            >
              <template #prefix>
                <el-icon><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>
        </div>

        <!-- 操作按钮 -->
        <el-form-item class="form-actions">
          <el-button
            v-if="step === 1"
            type="primary"
            :loading="sendingCode"
            @click="sendVerificationCode"
            class="action-btn"
          >
            发送验证码
          </el-button>

          <el-button
            v-if="step === 2"
            type="primary"
            :loading="verifying"
            @click="verifyEmailCode"
            class="action-btn"
          >
            验证邮箱
          </el-button>

          <el-button
            v-if="step === 3"
            type="primary"
            :loading="registering"
            @click="handleRegister"
            class="action-btn"
          >
            完成注册
          </el-button>

          <el-button
            v-if="step > 1"
            @click="goBack"
            class="back-btn"
          >
            上一步
          </el-button>
        </el-form-item>

        <!-- 登录链接 -->
        <div class="login-link">
          <span>已有账户？</span>
          <el-button type="text" @click="$emit('switch-to-login')">
            立即登录
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Message, Key, User, Lock } from '@element-plus/icons-vue'
import { sendVerificationCode as apiSendCode, verifyEmail, register } from '@/services/auth'

// 组件事件
const emit = defineEmits(['register-success', 'switch-to-login'])

// 响应式数据
const step = ref(1) // 1: 邮箱输入, 2: 验证码, 3: 用户信息
const sendingCode = ref(false)
const verifying = ref(false)
const registering = ref(false)
const countdown = ref(0)
const registerFormRef = ref()

// 表单数据
const registerForm = reactive({
  email: '',
  verificationCode: '',
  username: '',
  password: '',
  confirmPassword: ''
})

// 表单验证规则
const registerRules = computed(() => ({
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  verificationCode: step.value >= 2 ? [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码为6位数字', trigger: 'blur' }
  ] : [],
  password: step.value >= 3 ? [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ] : [],
  confirmPassword: step.value >= 3 ? [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ] : []
}))

// 发送验证码
const sendVerificationCode = async () => {
  try {
    // 验证邮箱
    await registerFormRef.value.validateField('email')
    
    sendingCode.value = true
    
    await apiSendCode(registerForm.email, '注册')
    
    ElMessage.success('验证码已发送到您的邮箱')
    step.value = 2
    startCountdown()
    
  } catch (error) {
    ElMessage.error(error.message || '发送验证码失败')
  } finally {
    sendingCode.value = false
  }
}

// 验证邮箱验证码
const verifyEmailCode = async () => {
  try {
    await registerFormRef.value.validateField('verificationCode')
    
    verifying.value = true
    
    await verifyEmail(registerForm.email, registerForm.verificationCode)
    
    ElMessage.success('邮箱验证成功')
    step.value = 3
    
  } catch (error) {
    ElMessage.error(error.message || '验证码验证失败')
  } finally {
    verifying.value = false
  }
}

// 完成注册
const handleRegister = async () => {
  try {
    await registerFormRef.value.validate()
    
    registering.value = true
    
    const userData = {
      email: registerForm.email,
      password: registerForm.password,
      username: registerForm.username || null,
      verificationCode: registerForm.verificationCode
    }
    
    const result = await register(userData)
    
    ElMessage.success('注册成功！')
    emit('register-success', result)
    
  } catch (error) {
    ElMessage.error(error.message || '注册失败')
  } finally {
    registering.value = false
  }
}

// 返回上一步
const goBack = () => {
  if (step.value > 1) {
    step.value--
  }
}

// 倒计时
const startCountdown = () => {
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}
</script>

<style scoped>
.register-form {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
}

.register-card {
  width: 100%;
  max-width: 400px;
}

.card-header {
  text-align: center;
}

.card-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.card-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.verification-step,
.user-info-step {
  margin-top: 20px;
}

.verification-input {
  display: flex;
  gap: 10px;
  align-items: center;
}

.verification-input .el-input {
  flex: 1;
}

.resend-btn {
  white-space: nowrap;
  padding: 0;
  min-width: 80px;
}

.verification-alert {
  margin-top: 10px;
}

.verification-alert .text-sm {
  font-size: 12px;
  color: #909399;
  margin: 4px 0 0 0;
}

.form-actions {
  margin-top: 30px;
}

.action-btn {
  width: 100%;
}

.back-btn {
  width: 100%;
  margin-top: 10px;
}

.login-link {
  text-align: center;
  margin-top: 20px;
  color: #909399;
  font-size: 14px;
}

.login-link .el-button {
  padding: 0;
  margin-left: 5px;
}
</style>

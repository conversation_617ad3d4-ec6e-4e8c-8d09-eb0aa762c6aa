import { createRouter, createWebHashHistory } from 'vue-router'
import TempMailBox from '../views/tempMail/index.vue'
import AdminPanel from '../views/adminPanel/index.vue'
import { useUserStore } from '../stores/user'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/home/<USER>'),
    meta: {
      title: '临时邮箱 - 首页'
    }
  },
  {
    path: '/apikey_mail',
    name: 'ApiKeyMail',
    component: TempMailBox,
    meta: {
      title: '授权密钥查询'
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/auth/Login.vue'),
    meta: {
      title: '用户登录'
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/auth/Register.vue'),
    meta: {
      title: '用户注册'
    }
  },
  {
    path: '/test-auth',
    name: 'TestAuth',
    component: () => import('../views/TestAuth.vue'),
    meta: {
      title: '邮件验证测试'
    }
  },
  {
    path: '/auth/github/callback',
    name: 'GitHubCallback',
    component: () => import('../views/auth/GitHubCallback.vue'),
    meta: {
      title: 'GitHub 登录处理中...'
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../views/user/Profile.vue'),
    meta: {
      title: '个人资料',
      requiresAuth: true
    }
  },
  {
    path: '/pricing',
    name: 'Pricing',
    component: () => import('../views/pricing/index.vue'),
    meta: {
      title: '配额购买'
    }
  },
  {
    path: '/user',
    redirect: '/profile'
  },
  {
    path: '/admin',
    name: 'Admin',
    component: AdminPanel,
    meta: {
      title: '后台管理'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫 - 设置页面标题和认证检查
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - TempMail.ofun.my`
  }

  // 获取用户store
  const userStore = useUserStore()

  // 检查是否需要认证
  if (to.meta?.requiresAuth) {
    if (!userStore.isLoggedIn) {
      // 未登录，重定向到登录页面
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }

  // 如果已登录用户访问登录或注册页面，重定向到首页
  if ((to.name === 'Login' || to.name === 'Register') && userStore.isLoggedIn) {
    next('/')
    return
  }

  next()
})

export default router

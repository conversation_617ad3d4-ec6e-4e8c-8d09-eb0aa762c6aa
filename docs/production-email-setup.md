# 生产环境邮件发送配置指南

## 概述

本指南基于 [Cloudflare Email Workers 最佳实践](https://developers.cloudflare.com/email-routing/email-workers/local-development/) 提供多种生产环境邮件发送方案。

## 推荐的邮件服务提供商

### 1. SendGrid（推荐）

**优势**: 可靠性高、API 简单、免费额度充足

**配置步骤**:
1. 注册 [SendGrid](https://sendgrid.com/) 账户
2. 创建 API Key
3. 验证发送域名
4. 配置 Cloudflare Secrets

```bash
# 设置 SendGrid API Key
wrangler secret put SENDGRID_API_KEY
```

**免费额度**: 每月 100 封邮件

### 2. Resend（推荐）

**优势**: 现代化 API、开发者友好、价格合理

**配置步骤**:
1. 注册 [Resend](https://resend.com/) 账户
2. 创建 API Key
3. 验证发送域名

```bash
# 设置 Resend API Key
wrangler secret put RESEND_API_KEY
```

**免费额度**: 每月 3,000 封邮件

### 3. Mailgun

**优势**: 功能强大、企业级可靠性

**配置步骤**:
1. 注册 [Mailgun](https://www.mailgun.com/) 账户
2. 创建 API Key 和域名
3. 验证发送域名

```bash
# 设置 Mailgun 配置
wrangler secret put MAILGUN_API_KEY
wrangler secret put MAILGUN_DOMAIN
```

**免费额度**: 每月 5,000 封邮件（前 3 个月）

### 4. Postmark

**优势**: 专注于事务性邮件、送达率高

**配置步骤**:
1. 注册 [Postmark](https://postmarkapp.com/) 账户
2. 创建 Server Token
3. 验证发送域名

```bash
# 设置 Postmark API Key
wrangler secret put POSTMARK_API_KEY
```

**免费额度**: 每月 100 封邮件

## 域名配置

### 1. DNS 记录配置

为了提高邮件送达率，需要配置以下 DNS 记录：

#### SPF 记录
```
TXT @ "v=spf1 include:sendgrid.net ~all"
```

#### DKIM 记录
根据邮件服务提供商的要求配置 DKIM 记录。

#### DMARC 记录
```
TXT _dmarc "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>"
```

### 2. Cloudflare Email Routing 配置

1. 在 Cloudflare Dashboard 中启用 Email Routing
2. 配置 Catch-all 规则指向你的 Worker
3. 验证域名所有权

## 部署配置

### 1. 设置生产环境 Secrets

选择一个邮件服务提供商并设置相应的 secrets：

```bash
# 选项 1: SendGrid
wrangler secret put SENDGRID_API_KEY

# 选项 2: Resend
wrangler secret put RESEND_API_KEY

# 选项 3: Mailgun
wrangler secret put MAILGUN_API_KEY
wrangler secret put MAILGUN_DOMAIN

# 选项 4: Postmark
wrangler secret put POSTMARK_API_KEY

# 其他必要的 secrets
wrangler secret put JWT_SECRET
wrangler secret put GITHUB_CLIENT_SECRET
```

### 2. 部署到生产环境

```bash
# 部署 Worker
wrangler deploy

# 运行生产数据库迁移
wrangler d1 migrations apply ofun-email-db
```

### 3. 验证邮件发送

```bash
# 测试邮件发送
curl -X POST "https://your-worker.your-subdomain.workers.dev/api/auth/send-verification" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","purpose":"测试"}'
```

## 邮件模板自定义

### 1. 修改邮件模板

编辑 `workers/src/utils/email.js` 中的邮件模板：

```javascript
generateVerificationEmailHTML(code, purpose) {
  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>邮箱验证码</title>
    <style>
        /* 自定义样式 */
    </style>
</head>
<body>
    <!-- 自定义邮件内容 -->
</body>
</html>`;
}
```

### 2. 品牌化配置

在 `wrangler.toml` 中添加品牌相关变量：

```toml
[vars]
BRAND_NAME = "ofun.my"
BRAND_COLOR = "#007bff"
SUPPORT_EMAIL = "<EMAIL>"
```

## 监控和日志

### 1. 邮件发送监控

系统会自动记录邮件发送日志到 `email_logs` 表：

```sql
SELECT 
  type,
  COUNT(*) as total,
  SUM(success) as successful,
  AVG(success) as success_rate
FROM email_logs 
WHERE created_at > datetime('now', '-24 hours')
GROUP BY type;
```

### 2. 错误监控

查看邮件发送错误：

```sql
SELECT * FROM email_logs 
WHERE success = 0 
ORDER BY created_at DESC 
LIMIT 10;
```

### 3. Cloudflare Analytics

在 Cloudflare Dashboard 中查看：
- Worker 请求统计
- 错误率监控
- 性能指标

## 故障排除

### 1. 邮件发送失败

**检查步骤**:
1. 验证 API Key 是否正确
2. 检查域名验证状态
3. 查看 Worker 日志
4. 验证 DNS 配置

### 2. 邮件进入垃圾箱

**解决方案**:
1. 配置 SPF、DKIM、DMARC 记录
2. 使用已验证的发送域名
3. 避免垃圾邮件关键词
4. 保持良好的发送声誉

### 3. API 限制

**处理方法**:
1. 实现重试机制
2. 监控 API 使用量
3. 升级服务计划
4. 配置多个邮件服务商作为备用

## 成本优化

### 1. 免费额度利用

- SendGrid: 100 封/月
- Resend: 3,000 封/月  
- Mailgun: 5,000 封/月（前 3 个月）
- Postmark: 100 封/月

### 2. 多服务商配置

配置多个邮件服务商，根据使用量自动切换：

```javascript
// 在 email.js 中实现智能切换逻辑
async sendEmailProduction(to, subject, text, html) {
  const providers = [
    { name: 'resend', limit: 3000 },
    { name: 'sendgrid', limit: 100 },
    { name: 'mailgun', limit: 5000 }
  ];
  
  // 根据当月使用量选择提供商
  for (const provider of providers) {
    if (await this.checkMonthlyLimit(provider.name, provider.limit)) {
      return await this.sendViaProvider(provider.name, to, subject, text, html);
    }
  }
}
```

## 安全最佳实践

1. **API Key 安全**: 使用 Cloudflare Secrets 存储敏感信息
2. **频率限制**: 实现邮件发送频率限制
3. **内容过滤**: 验证邮件内容，防止滥用
4. **日志记录**: 记录所有邮件发送活动
5. **监控告警**: 设置异常发送监控

## 总结

推荐的生产环境配置：

1. **首选**: Resend（免费额度最高，API 现代化）
2. **备选**: SendGrid（老牌稳定，文档完善）
3. **企业**: Mailgun（功能强大，适合大规模使用）

按照本指南配置后，你的邮件系统将具备：
- 高可靠性的邮件发送
- 良好的送达率
- 完整的监控和日志
- 成本优化的多服务商支持

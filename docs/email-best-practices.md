# Cloudflare Email Workers 最佳实践

## 概述

本文档基于 [Cloudflare Email Workers 官方文档](https://developers.cloudflare.com/email-routing/email-workers/local-development/) 整理了邮件接收和发送的最佳实践。

## 1. 邮件接收配置

### 1.1 域名配置

确保你的域名 `ofun.my` 已经：

1. **添加到 Cloudflare**
2. **启用 Email Routing**
3. **配置 Catch-all 规则**

```bash
# 在 Cloudflare Dashboard 中配置
Domain: ofun.my
Catch-all: 启用
Destination: ofun-email-unified (Worker)
```

### 1.2 DNS 记录

Cloudflare 会自动添加必要的 MX 记录：

```
ofun.my MX 10 isaac.mx.cloudflare.net
ofun.my MX 20 linda.mx.cloudflare.net
ofun.my MX 30 amir.mx.cloudflare.net
```

### 1.3 Worker 邮件处理

```javascript
// workers/src/handlers/email.js
export default {
  async email(message, env, ctx) {
    // 解析邮件
    const parsedEmail = await parseEmailWithPostalMime(message.raw);
    
    // 提取验证码
    const verificationCode = extractVerificationCode(parsedEmail.text);
    
    // 保存到数据库
    await saveEmailToDatabase(env, message, parsedEmail, verificationCode);
    
    return new Response('OK');
  }
}
```

## 2. 邮件发送配置

### 2.1 本地开发环境

#### 使用 MailHog

```bash
# 安装 MailHog
brew install mailhog  # macOS
# 或下载二进制文件

# 启动 MailHog
mailhog

# Web 界面: http://localhost:8025
# SMTP: localhost:1025
```

#### 配置示例

```javascript
// 本地开发配置
const localConfig = {
  smtp: {
    host: 'localhost',
    port: 1025,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: 'password'
    }
  }
};
```

### 2.2 生产环境

#### 使用第三方 SMTP 服务

推荐的 SMTP 服务提供商：

1. **SendGrid**
2. **Mailgun**
3. **Amazon SES**
4. **Postmark**

#### SendGrid 配置示例

```javascript
// 生产环境配置
const productionConfig = {
  apiKey: env.SENDGRID_API_KEY,
  from: '<EMAIL>',
  apiUrl: 'https://api.sendgrid.com/v3/mail/send'
};

async function sendEmailViaSendGrid(to, subject, text, html) {
  const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${env.SENDGRID_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      personalizations: [{
        to: [{ email: to }]
      }],
      from: { email: '<EMAIL>' },
      subject: subject,
      content: [
        { type: 'text/plain', value: text },
        { type: 'text/html', value: html }
      ]
    })
  });
  
  return response.ok;
}
```

## 3. 用户注册邮箱验证流程

### 3.1 完整流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant W as Worker
    participant D as 数据库
    participant E as 邮件服务

    U->>F: 输入邮箱注册
    F->>W: POST /api/auth/send-verification
    W->>D: 保存验证码
    W->>E: 发送验证邮件
    E-->>U: 收到验证码邮件
    U->>F: 输入验证码
    F->>W: POST /api/auth/verify-email
    W->>D: 验证码校验
    W->>F: 验证成功
    F->>W: POST /api/auth/register
    W->>D: 创建用户
    W->>F: 注册成功
```

### 3.2 API 接口

#### 发送验证码

```bash
POST /api/auth/send-verification
Content-Type: application/json

{
  "email": "<EMAIL>",
  "purpose": "注册"
}
```

#### 验证邮箱

```bash
POST /api/auth/verify-email
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456"
}
```

#### 用户注册

```bash
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "username": "username",
  "verificationCode": "123456"
}
```

## 4. 安全最佳实践

### 4.1 验证码安全

```javascript
// 验证码配置
const VERIFICATION_CONFIG = {
  length: 6,                    // 验证码长度
  expireMinutes: 10,           // 过期时间
  maxAttempts: 5,              // 最大尝试次数
  rateLimitMinutes: 1,         // 发送频率限制
  cleanupHours: 24             // 清理过期记录
};

// 生成安全的验证码
function generateSecureCode(length = 6) {
  const digits = '0123456789';
  let code = '';
  for (let i = 0; i < length; i++) {
    code += digits[Math.floor(Math.random() * digits.length)];
  }
  return code;
}
```

### 4.2 防止滥用

```javascript
// 频率限制
async function checkRateLimit(env, email) {
  const recent = await env.DB.prepare(`
    SELECT COUNT(*) as count 
    FROM email_verifications 
    WHERE email = ? AND created_at > datetime('now', '-1 minute')
  `).bind(email).first();
  
  return recent.count < 1;
}

// IP 限制
async function checkIPLimit(env, ip) {
  const recent = await env.DB.prepare(`
    SELECT COUNT(*) as count 
    FROM email_verifications 
    WHERE ip_address = ? AND created_at > datetime('now', '-5 minutes')
  `).bind(ip).first();
  
  return recent.count < 5;
}
```

### 4.3 邮件模板安全

```javascript
// 防止 XSS 的邮件模板
function sanitizeEmailContent(content) {
  return content
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;');
}

// 验证码邮件模板
function generateVerificationEmail(code, purpose) {
  const sanitizedPurpose = sanitizeEmailContent(purpose);
  const sanitizedCode = sanitizeEmailContent(code);
  
  return {
    subject: `【ofun.my】邮箱验证码`,
    html: `
      <div style="font-family: Arial, sans-serif;">
        <h2>邮箱验证</h2>
        <p>您正在进行${sanitizedPurpose}操作，验证码为：</p>
        <div style="font-size: 24px; font-weight: bold; color: #007bff;">
          ${sanitizedCode}
        </div>
        <p>验证码有效期为 10 分钟，请勿泄露给他人。</p>
      </div>
    `
  };
}
```

## 5. 监控和日志

### 5.1 邮件发送监控

```javascript
// 邮件发送日志
async function logEmailSent(env, email, type, success, error = null) {
  await env.DB.prepare(`
    INSERT INTO email_logs (email, type, success, error, created_at)
    VALUES (?, ?, ?, ?, datetime('now'))
  `).bind(email, type, success ? 1 : 0, error).run();
}

// 使用示例
try {
  await sendEmail(email, subject, content);
  await logEmailSent(env, email, 'verification', true);
} catch (error) {
  await logEmailSent(env, email, 'verification', false, error.message);
}
```

### 5.2 性能监控

```javascript
// 邮件发送性能监控
async function sendEmailWithMetrics(env, email, subject, content) {
  const startTime = Date.now();
  
  try {
    const result = await sendEmail(email, subject, content);
    const duration = Date.now() - startTime;
    
    // 记录成功指标
    console.log(`Email sent successfully in ${duration}ms`);
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    
    // 记录失败指标
    console.error(`Email failed after ${duration}ms:`, error);
    
    throw error;
  }
}
```

## 6. 故障排除

### 6.1 常见问题

1. **邮件未收到**
   - 检查垃圾邮件文件夹
   - 验证 DNS 配置
   - 检查 Worker 日志

2. **验证码无效**
   - 检查时间同步
   - 验证数据库连接
   - 确认验证码格式

3. **发送频率限制**
   - 检查频率限制逻辑
   - 调整限制参数
   - 实现用户友好的错误提示

### 6.2 调试工具

```bash
# 查看 Worker 日志
wrangler tail

# 测试邮件接收
curl -X POST "https://your-worker.your-subdomain.workers.dev" \
  -H "Content-Type: application/json" \
  -d '{"test": "email"}'

# 测试数据库连接
wrangler d1 execute your-database --command "SELECT 1"
```

## 7. 部署清单

### 7.1 生产环境部署前检查

- [ ] DNS 记录配置正确
- [ ] Email Routing 启用
- [ ] Worker 部署成功
- [ ] 数据库迁移完成
- [ ] 环境变量配置
- [ ] SMTP 服务配置
- [ ] 监控和日志配置
- [ ] 安全策略实施
- [ ] 性能测试通过

### 7.2 部署命令

```bash
# 部署 Worker
wrangler deploy

# 运行生产数据库迁移
wrangler d1 migrations apply ofun-email-db

# 设置生产环境变量
wrangler secret put SENDGRID_API_KEY
wrangler secret put JWT_SECRET
```

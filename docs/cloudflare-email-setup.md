# Cloudflare 全家桶邮件发送配置指南

## 概述

本指南基于 [Cloudflare Email Workers 官方文档](https://developers.cloudflare.com/email-routing/email-workers/local-development/) 实现纯 Cloudflare 全家桶的邮件发送方案。

## 配置方案

### 方案 1: Cloudflare Send Email API (推荐)

这是 Cloudflare 最新推出的邮件发送 API，专为 Workers 设计。

#### 1.1 启用 Send Email API

在 Cloudflare Dashboard 中：
1. 进入你的 Workers & Pages
2. 选择你的 Worker
3. 进入 **Settings** > **Variables**
4. 添加 **Send Email** binding

#### 1.2 更新 wrangler.toml

```toml
# 启用 Send Email API (按照官方示例)
[[send_email]]
name = "SEND_EMAIL"

# 本地环境也需要配置
[[env.local.send_email]]
name = "SEND_EMAIL"
```

#### 1.3 代码实现

邮件发送代码已按照官方示例实现：

```javascript
// 构建邮件消息，按照官方示例格式
const message = {
  from: {
    email: from,
    name: "ofun.my"
  },
  to: [
    {
      email: to
    }
  ],
  subject: subject,
  content: [
    {
      type: "text/plain",
      value: text
    },
    {
      type: "text/html",
      value: html
    }
  ]
};

// 发送邮件
const response = await this.env.SEND_EMAIL.send(message);
```

#### 1.4 域名验证

1. 在 Cloudflare Dashboard 中验证你的发送域名 `ofun.my`
2. 配置 SPF 记录：
```dns
ofun.my TXT "v=spf1 include:_spf.cloudflare.net ~all"
```

### 方案 2: Email Routing + Worker 转发

使用 Cloudflare Email Routing 的转发功能。

#### 2.1 启用 Email Routing

1. 在 Cloudflare Dashboard 中启用 Email Routing
2. 配置 Catch-all 规则指向你的 Worker
3. 设置转发规则

#### 2.2 配置环境变量

```bash
# 设置 Cloudflare API 凭据
wrangler secret put CLOUDFLARE_ACCOUNT_ID
wrangler secret put CLOUDFLARE_API_TOKEN

# 启用 Email Routing
wrangler secret put EMAIL_ROUTING_ENABLED
# 输入: true
```

#### 2.3 DNS 配置

Cloudflare 会自动添加 MX 记录：
```dns
ofun.my MX 10 isaac.mx.cloudflare.net
ofun.my MX 20 linda.mx.cloudflare.net
ofun.my MX 30 amir.mx.cloudflare.net
```

### 方案 3: SMTP Gateway (自定义)

创建一个 SMTP 网关 Worker 来处理邮件发送。

#### 3.1 创建 SMTP Gateway Worker

```javascript
// smtp-gateway.js
export default {
  async fetch(request, env) {
    if (request.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 });
    }

    const emailData = await request.json();
    
    // 实现 SMTP 发送逻辑
    // 可以集成第三方 SMTP 服务或使用 Cloudflare 内部 API
    
    return new Response(JSON.stringify({ success: true }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
```

#### 3.2 配置 Gateway URL

```bash
wrangler secret put CLOUDFLARE_SMTP_GATEWAY
# 输入: https://your-smtp-gateway.your-subdomain.workers.dev
```

## 本地开发配置

### 更新本地环境变量

在 `wrangler.toml` 的 `[env.local.vars]` 中添加：

```toml
[env.local.vars]
EMAIL_ROUTING_ENABLED = "false"
CLOUDFLARE_SMTP_GATEWAY = "http://localhost:8788"
```

### 本地 SMTP 模拟

本地开发时，邮件会输出到控制台，不会真实发送。

## 生产环境部署

### 1. 选择邮件发送方案

推荐按优先级选择：

1. **Send Email API** (最简单，推荐)
2. **Email Routing** (功能强大)
3. **SMTP Gateway** (最灵活)

### 2. 配置域名和 DNS

#### SPF 记录
```dns
ofun.my TXT "v=spf1 include:_spf.cloudflare.net ~all"
```

#### DKIM 记录
Cloudflare 会自动管理 DKIM 签名。

#### DMARC 记录
```dns
_dmarc.ofun.my TXT "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>"
```

### 3. 部署配置

```bash
# 如果使用 Send Email API
# 在 Cloudflare Dashboard 中配置 binding

# 如果使用 Email Routing
wrangler secret put CLOUDFLARE_ACCOUNT_ID
wrangler secret put CLOUDFLARE_API_TOKEN

# 部署 Worker
wrangler deploy
```

## 邮件模板和内容

### HTML 邮件模板

系统会自动生成符合标准的 HTML 邮件：

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>邮箱验证码</title>
    <style>
        body { font-family: Arial, sans-serif; }
        .container { max-width: 600px; margin: 0 auto; }
        .code { font-size: 32px; font-weight: bold; color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>ofun.my 邮箱验证</h1>
        <p>验证码：<span class="code">123456</span></p>
    </div>
</body>
</html>
```

### 纯文本邮件

同时提供纯文本版本以确保兼容性：

```
ofun.my 邮箱验证

您好！

您正在进行注册操作，请使用以下验证码完成验证：

验证码：123456

注意事项：
- 验证码有效期为 10 分钟
- 请勿将验证码告诉他人
```

## 监控和日志

### 邮件发送日志

系统会记录所有邮件发送活动：

```sql
SELECT 
  type,
  COUNT(*) as total,
  SUM(success) as successful,
  AVG(success) as success_rate
FROM email_logs 
WHERE created_at > datetime('now', '-24 hours')
GROUP BY type;
```

### Cloudflare Analytics

在 Cloudflare Dashboard 中查看：
- Worker 请求统计
- 邮件发送成功率
- 错误日志和性能指标

### 实时监控

```bash
# 查看实时日志
wrangler tail

# 过滤邮件相关日志
wrangler tail | grep -i email
```

## 故障排除

### 常见问题

1. **Send Email API 未配置**
   ```
   错误: this.env.SEND_EMAIL is undefined
   解决: 在 Cloudflare Dashboard 中添加 Send Email binding
   ```

2. **域名未验证**
   ```
   错误: Domain not verified
   解决: 在 Cloudflare Dashboard 中验证发送域名
   ```

3. **Email Routing 未启用**
   ```
   错误: Email Routing not enabled
   解决: 在 Cloudflare Dashboard 中启用 Email Routing
   ```

### 调试步骤

1. **检查 Worker 日志**
   ```bash
   wrangler tail
   ```

2. **验证 DNS 配置**
   ```bash
   dig TXT ofun.my | grep spf
   dig MX ofun.my
   ```

3. **测试邮件发送**
   ```bash
   curl -X POST "https://your-worker.workers.dev/api/auth/send-verification" \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","purpose":"测试"}'
   ```

## 性能优化

### 降级处理

系统实现了智能降级：

1. **Send Email API** → **Email Routing** → **SMTP Gateway** → **日志记录**
2. 即使邮件发送失败，也不会阻塞用户注册流程
3. 所有邮件活动都会记录到数据库

### 缓存和限流

```javascript
// 实现邮件发送频率限制
const rateLimiter = {
  async checkLimit(email) {
    const key = `email_limit:${email}`;
    const count = await env.KV.get(key);
    if (count && parseInt(count) >= 5) {
      throw new Error('发送频率过高，请稍后再试');
    }
    await env.KV.put(key, (parseInt(count) || 0) + 1, { expirationTtl: 3600 });
  }
};
```

## 安全最佳实践

1. **API 密钥安全**: 使用 Cloudflare Secrets 存储敏感信息
2. **域名验证**: 确保只能从验证的域名发送邮件
3. **内容过滤**: 验证邮件内容，防止垃圾邮件
4. **频率限制**: 防止邮件发送滥用
5. **日志审计**: 记录所有邮件发送活动

## 总结

推荐的 Cloudflare 全家桶配置：

1. **首选**: Send Email API (最简单，官方推荐)
2. **备选**: Email Routing (功能强大，适合复杂场景)
3. **自定义**: SMTP Gateway (最灵活，适合特殊需求)

所有方案都支持：
- 完整的错误处理和降级
- 详细的日志记录和监控
- 符合标准的邮件格式
- 高可用性和性能优化

按照本指南配置后，你将拥有一个完全基于 Cloudflare 的高可靠邮件发送系统！

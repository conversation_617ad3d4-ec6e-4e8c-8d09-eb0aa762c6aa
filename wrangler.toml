# Cloudflare Workers 统一配置文件
# 合并了 API 服务和邮件处理器

name = "ofun-email-unified"
main = "workers/src/index.js"
compatibility_date = "2024-01-01"

# D1 数据库配置
[[d1_databases]]
binding = "DB"
database_name = "ofun-email-db"
database_id = "25f4e059-5ad7-46a2-8e23-8f9cf55baded"

# 本地数据库配置（用于 wrangler 命令）
[[d1_databases]]
binding = "DB_LOCAL"
database_name = "ofun-email-db-local"
database_id = "d2064ab4-43ce-4313-80ae-33adbcab0f3c"



# 本地开发环境配置
[env.local]
name = "ofun-email-unified-local"

# 本地环境数据库配置
[[env.local.d1_databases]]
binding = "DB"
database_name = "ofun-email-db-local"
database_id = "d2064ab4-43ce-4313-80ae-33adbcab0f3c"

# 本地环境 Send Email 配置
[[env.local.send_email]]
name = "SEND_EMAIL"

# 本地环境变量
[env.local.vars]
ENVIRONMENT = "local"
ADMIN_PASSWORD = "admin123"
GITHUB_REDIRECT_URI = "http://localhost:8787/api/auth/github/callback"
GITHUB_CLIENT_ID = "your-local-github-client-id"
GITHUB_CLIENT_SECRET = "your-local-github-client-secret"
JWT_SECRET = "your-local-jwt-secret-key"
SMTP_HOST = "localhost"
SMTP_PORT = "1025"
SMTP_USER = "<EMAIL>"
SMTP_PASS = "password"
DOMAIN = "ofun.my"

# 生产环境变量
[vars]
ENVIRONMENT = "production"
ADMIN_PASSWORD = "your-admin-password-here"
GITHUB_REDIRECT_URI = "https://tempmail.htmljs.cn/auth/github/callback"
DOMAIN = "ofun.my"

# GitHub OAuth 配置（需要在 Cloudflare Dashboard 中设置为 secrets）
GITHUB_CLIENT_ID = "********************"
GITHUB_CLIENT_SECRET = "c3a6bc53c55b1da20f11183bfc76b2641e11796b"

# Cloudflare Send Email API 配置
# 按照官方示例: https://developers.cloudflare.com/email-routing/email-workers/send-email-workers/#example-worker
[[send_email]]
name = "SEND_EMAIL"

# 可选：Webhook 通知 URL
# WEBHOOK_URL = "https://your-webhook-endpoint.com/notify"

# 日志分析配置
[observability]
enabled = true
head_sampling_rate = 1

# 🚀 ofun-email-unified 快速启动指南

## ✅ 环境已配置完成

你的本地开发环境已经配置完成！以下是快速启动步骤：

## 1. 启动开发服务器

```bash
# 启动 Worker 开发服务器
npm run dev:worker

# 或者使用完整的开发脚本（包括 MailHog）
npm run dev:start
```

服务器将在 http://localhost:8787 启动

## 2. 测试邮件功能

### 快速测试
```bash
# 测试完整的注册流程
npm run dev:test-flow
```

### 手动测试
```bash
# 1. 发送验证码
curl -X POST "http://localhost:8787/api/auth/send-verification" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","purpose":"注册"}'

# 2. 查看控制台获取验证码，然后验证
curl -X POST "http://localhost:8787/api/auth/verify-email" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","code":"YOUR_CODE"}'

# 3. 注册用户
curl -X POST "http://localhost:8787/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","username":"testuser","verificationCode":"YOUR_CODE"}'
```

## 3. 查看邮件

### 控制台输出
验证码和邮件内容会直接显示在 Wrangler 开发服务器的控制台中。

### MailHog（可选）
如果安装了 MailHog：
```bash
# 启动 MailHog
mailhog

# 访问 Web 界面
open http://localhost:8025
```

## 4. 数据库管理

```bash
# 查看数据库表
wrangler d1 execute ofun-email-db-local --command "SELECT name FROM sqlite_master WHERE type='table';"

# 查看用户
wrangler d1 execute ofun-email-db-local --command "SELECT * FROM users;"

# 查看验证码记录
wrangler d1 execute ofun-email-db-local --command "SELECT * FROM email_verifications ORDER BY created_at DESC LIMIT 5;"
```

## 5. API 端点

### 认证相关
- `POST /api/auth/send-verification` - 发送验证码
- `POST /api/auth/verify-email` - 验证邮箱
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出

### 用户相关
- `GET /api/user/profile` - 获取用户信息
- `POST /api/redemption/redeem` - 兑换码兑换

## 6. 前端集成

### 使用认证服务
```javascript
import { sendVerificationCode, verifyEmail, register, login } from '@/services/auth'

// 发送验证码
await sendVerificationCode('<EMAIL>', '注册')

// 验证邮箱
await verifyEmail('<EMAIL>', '123456')

// 注册用户
await register({
  email: '<EMAIL>',
  password: 'password123',
  username: 'username',
  verificationCode: '123456'
})
```

### 使用注册组件
```vue
<template>
  <RegisterForm 
    @register-success="handleRegisterSuccess"
    @switch-to-login="showLogin = true"
  />
</template>

<script setup>
import RegisterForm from '@/components/RegisterForm.vue'

const handleRegisterSuccess = (result) => {
  console.log('注册成功:', result)
  // 处理注册成功逻辑
}
</script>
```

## 7. 配置说明

### 本地环境变量
配置文件：`.env.local`（自动生成）
```env
ENVIRONMENT=local
JWT_SECRET=your-local-jwt-secret-key
DOMAIN=ofun.my
SMTP_HOST=localhost
SMTP_PORT=1025
```

### Wrangler 配置
配置文件：`wrangler.toml`
- 本地环境：`[env.local]`
- 生产环境：默认配置

## 8. 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库是否存在
   wrangler d1 list
   
   # 重新创建表
   wrangler d1 execute ofun-email-db-local --file=migrations/0001_initial.sql
   ```

2. **验证码无效**
   - 检查验证码是否过期（10分钟）
   - 确认验证码是否已使用
   - 查看控制台输出的验证码

3. **邮件发送失败**
   - 检查控制台错误日志
   - 确认 SMTP 配置（本地环境使用控制台输出）

### 重置环境
```bash
# 删除本地数据库
rm -rf .wrangler/state/v3/d1/

# 重新运行迁移
wrangler d1 execute ofun-email-db-local --file=migrations/0001_initial.sql
wrangler d1 execute ofun-email-db-local --command "CREATE TABLE IF NOT EXISTS email_verifications (id INTEGER PRIMARY KEY AUTOINCREMENT, email TEXT NOT NULL, code TEXT NOT NULL, expires_at DATETIME NOT NULL, used INTEGER DEFAULT 0, created_at DATETIME DEFAULT CURRENT_TIMESTAMP);"
```

## 9. 生产部署

### 准备部署
```bash
# 构建前端
npm run build

# 部署 Worker
wrangler deploy

# 运行生产迁移
npm run db:migrate:prod
```

### 设置生产环境变量
```bash
wrangler secret put JWT_SECRET
wrangler secret put SENDGRID_API_KEY  # 如果使用 SendGrid
```

## 10. 下一步

1. **配置真实的 SMTP 服务**（SendGrid、Mailgun 等）
2. **设置 GitHub OAuth**（可选）
3. **配置域名邮件接收**（Cloudflare Email Routing）
4. **添加更多邮件模板**
5. **实现邮件统计和监控**

## 📚 更多文档

- [完整开发指南](README-DEV.md)
- [邮件最佳实践](docs/email-best-practices.md)
- [API 文档](docs/api.md)

## 🎯 测试清单

- [x] 本地开发环境启动
- [x] 数据库连接正常
- [x] 验证码发送功能
- [x] 邮箱验证功能
- [x] 用户注册功能
- [x] 用户登录功能
- [x] 前端组件集成
- [ ] MailHog 邮件查看（可选）
- [ ] 生产环境部署（待配置）

恭喜！你的邮件验证系统已经可以正常工作了！🎉

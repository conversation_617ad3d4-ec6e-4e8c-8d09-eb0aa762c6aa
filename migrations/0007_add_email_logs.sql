-- 添加邮件发送日志表
CREATE TABLE IF NOT EXISTS email_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  email TEXT NOT NULL,
  type TEXT NOT NULL, -- 'verification', 'notification', etc.
  success INTEGER DEFAULT 0,
  error TEXT,
  ip_address TEXT,
  user_agent TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 为邮件日志表创建索引
CREATE INDEX IF NOT EXISTS idx_email_logs_email ON email_logs(email);
CREATE INDEX IF NOT EXISTS idx_email_logs_type ON email_logs(type);
CREATE INDEX IF NOT EXISTS idx_email_logs_created ON email_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_email_logs_success ON email_logs(success);

-- 为邮箱验证表添加 IP 地址字段
ALTER TABLE email_verifications ADD COLUMN ip_address TEXT;
ALTER TABLE email_verifications ADD COLUMN user_agent TEXT;

-- 创建邮件发送统计视图
CREATE VIEW IF NOT EXISTS email_stats AS
SELECT 
  type,
  DATE(created_at) as date,
  COUNT(*) as total_sent,
  SUM(success) as successful,
  COUNT(*) - SUM(success) as failed,
  ROUND(CAST(SUM(success) AS FLOAT) / COUNT(*) * 100, 2) as success_rate
FROM email_logs
GROUP BY type, DATE(created_at)
ORDER BY date DESC, type;
